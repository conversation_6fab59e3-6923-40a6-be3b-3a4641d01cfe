const KeyCodeMap = {
    "KC_NO": {
      "code": "00 00",
      "name": "NO",
      "omit_name": "NO"
    },
    "KC_TRANSPARENT": {
      "code": "00 01",
      "name": "▿",
      "omit_name": "▿"
    },
    "KC_A": {
      "code": "00 04",
      "name": "A",
      "omit_name": "A"
    },
    "KC_B": {
      "code": "00 05",
      "name": "B",
      "omit_name": "B"
    },
    "KC_C": {
      "code": "00 06",
      "name": "<PERSON>",
      "omit_name": "C"
    },
    "KC_D": {
      "code": "00 07",
      "name": "D",
      "omit_name": "D"
    },
    "KC_E": {
      "code": "00 08",
      "name": "E",
      "omit_name": "E"
    },
    "KC_F": {
      "code": "00 09",
      "name": "F",
      "omit_name": "F"
    },
    "KC_G": {
      "code": "00 0A",
      "name": "<PERSON>",
      "omit_name": "G"
    },
    "KC_H": {
      "code": "00 0B",
      "name": "H",
      "omit_name": "H"
    },
    "KC_I": {
      "code": "00 0C",
      "name": "I",
      "omit_name": "I"
    },
    "KC_J": {
      "code": "00 0D",
      "name": "J",
      "omit_name": "J"
    },
    "KC_K": {
      "code": "00 0E",
      "name": "K",
      "omit_name": "K"
    },
    "KC_L": {
      "code": "00 0F",
      "name": "L",
      "omit_name": "L"
    },
    "KC_M": {
      "code": "00 10",
      "name": "M",
      "omit_name": "M"
    },
    "KC_N": {
      "code": "00 11",
      "name": "N",
      "omit_name": "N"
    },
    "KC_O": {
      "code": "00 12",
      "name": "O",
      "omit_name": "O"
    },
    "KC_P": {
      "code": "00 13",
      "name": "P",
      "omit_name": "P"
    },
    "KC_Q": {
      "code": "00 14",
      "name": "Q",
      "omit_name": "Q"
    },
    "KC_R": {
      "code": "00 15",
      "name": "R",
      "omit_name": "R"
    },
    "KC_S": {
      "code": "00 16",
      "name": "S",
      "omit_name": "S"
    },
    "KC_T": {
      "code": "00 17",
      "name": "T",
      "omit_name": "T"
    },
    "KC_U": {
      "code": "00 18",
      "name": "U",
      "omit_name": "U"
    },
    "KC_V": {
      "code": "00 19",
      "name": "V",
      "omit_name": "V"
    },
    "KC_W": {
      "code": "00 1A",
      "name": "W",
      "omit_name": "W"
    },
    "KC_X": {
      "code": "00 1B",
      "name": "X"
    },
    "KC_Y": {
      "code": "00 1C",
      "name": "Y"
    },
    "KC_Z": {
      "code": "00 1D",
      "name": "Z"
    },
    "KC_1": {
      "code": "00 1E",
      "name": "1 !"
    },
    "KC_2": {
      "code": "00 1F",
      "name": "2 @"
    },
    "KC_3": {
      "code": "00 20",
      "name": "3 #"
    },
    "KC_4": {
      "code": "00 21",
      "name": "4 $"
    },
    "KC_5": {
      "code": "00 22",
      "name": "5 %"
    },
    "KC_6": {
      "code": "00 23",
      "name": "6 ^"
    },
    "KC_7": {
      "code": "00 24",
      "name": "7 &"
    },
    "KC_8": {
      "code": "00 25",
      "name": "8 *"
    },
    "KC_9": {
      "code": "00 26",
      "name": "9 ("
    },
    "KC_0": {
      "code": "00 27",
      "name": "0 )"
    },
    "KC_ENTER": {
      "code": "00 28",
      "name": "Enter"
    },
    "KC_ESCAPE": {
      "code": "00 29",
      "name": "Esc"
    },
    "KC_BACKSPACE": {
      "code": "00 2A",
      "name": "Backspace"
    },
    "KC_TAB": {
      "code": "00 2B",
      "name": "Tab"
    },
    "KC_GRAVE": {
      "code": "00 35",
      "name": "~ .",
      "omit_name": "~"
    },
    "KC_F1": {
      "code": "00 3A",
      "name": "F1"
    },
    "KC_F2": {
      "code": "00 3B",
      "name": "F2"
    },
    "KC_F3": {
      "code": "00 3C",
      "name": "F3"
    },
    "KC_F4": {
      "code": "00 3D",
      "name": "F4"
    },
    "KC_F5": {
      "code": "00 3E",
      "name": "F5"
    },
    "KC_F6": {
      "code": "00 3F",
      "name": "F6"
    },
    "KC_F7": {
      "code": "00 40",
      "name": "F7"
    },
    "KC_F8": {
      "code": "00 41",
      "name": "F8"
    },
    "KC_F9": {
      "code": "00 42",
      "name": "F9"
    },
    "KC_F10": {
      "code": "00 43",
      "name": "F10"
    },
    "KC_F11": {
      "code": "00 44",
      "name": "F11"
    },
    "KC_F12": {
      "code": "00 45",
      "name": "F12"
    },
    "KC_F13": {
      "code": "00 68",
      "name": "F13"
    },
    "KC_F14": {
      "code": "00 69",
      "name": "F14"
    },
    "KC_F15": {
      "code": "00 6A",
      "name": "F15"
    },
    "KC_F16": {
      "code": "00 6B",
      "name": "F16"
    },
    "KC_F17": {
      "code": "00 6C",
      "name": "F17"
    },
    "KC_F18": {
      "code": "00 6D",
      "name": "F18"
    },
    "KC_F19": {
      "code": "00 6E",
      "name": "F19"
    },
    "KC_F20": {
      "code": "00 6F",
      "name": "F20"
    },
    "KC_F21": {
      "code": "00 70",
      "name": "F21"
    },
    "KC_F22": {
      "code": "00 71",
      "name": "F22"
    },
    "KC_F23": {
      "code": "00 72",
      "name": "F23"
    },
    "KC_F24": {
      "code": "00 73",
      "name": "F24"
    },
    "KC_RIGHT": {
      "code": "00 4F",
      "name": "→"
    },
    "KC_LEFT": {
      "code": "00 50",
      "name": "←"
    },
    "KC_DOWN": {
      "code": "00 51",
      "name": "↓"
    },
    "KC_UP": {
      "code": "00 52",
      "name": "↑"
    },
    "KC_CAPS_LOCK": {
      "code": "00 39",
      "name": "CapsLock"
    },
    "KC_PRINT_SCREEN": {
      "code": "00 46",
      "name": "Print Screen"
    },
    "KC_SCROLL_LOCK": {
      "code": "00 47",
      "name": "Scroll Lock"
    },
    "KC_PAUSE": {
      "code": "00 48",
      "name": "Pause"
    },
    "KC_INSERT": {
      "code": "00 49",
      "name": "Insert"
    },
    "KC_HOME": {
      "code": "00 4A",
      "name": "Home"
    },
    "KC_PAGE_UP": {
      "code": "00 4B",
      "name": "Page Up"
    },
    "KC_DELETE": {
      "code": "00 4C",
      "name": "Delete"
    },
    "KC_END": {
      "code": "00 4D",
      "name": "End"
    },
    "KC_PAGE_DOWN": {
      "code": "00 4E",
      "name": "Page Down"
    },
    "KC_SPACE": {
      "code": "00 2C",
      "name": "Space"
    },
    "KC_MINUS": {
      "code": "00 2D",
      "name": "- _"
    },
    "KC_EQUAL": {
      "code": "00 2E",
      "name": "= +"
    },
    "KC_BACKSLASH": {
      "code": "00 31",
      "name": "\\"
    },
    "KC_RIGHT_BRACKET": {
      "code": "00 30",
      "name": "]"
    },
    "KC_LEFT_BRACKET": {
      "code": "00 2F",
      "name": "["
    },
    "KC_LEFT_CTRL": {
      "code": "00 E0",
      "name": "L-Ctrl"
    },
    "KC_LEFT_SHIFT": {
      "code": "00 E1",
      "name": "L-Shift"
    },
    "KC_LEFT_ALT": {
      "code": "00 E2",
      "name": "L-Alt"
    },
    "KC_LEFT_GUI": {
      "code": "00 E3",
      "name": "L-Win"
    },
    "KC_RIGHT_CTRL": {
      "code": "00 E4",
      "name": "R-Ctrl"
    },
    "KC_RIGHT_SHIFT": {
      "code": "00 E5",
      "name": "R-Shift"
    },
    "KC_RIGHT_ALT": {
      "code": "00 E6",
      "name": "R-Alt"
    },
    "KC_RIGHT_GUI": {
      "code": "00 E7",
      "name": "R-Win"
    },
    "KC_NONUS_HASH": {
      "code": "00 35",
      "name": "#"
    },
    "KC_SEMICOLON": {
      "code": "00 33",
      "name": ";"
    },
    "KC_QUOTE": {
      "code": "00 34",
      "name": "\" \'"
    },
    "KC_COMMA": {
      "code": "00 36",
      "name": ","
    },
    "KC_DOT": {
      "code": "00 37",
      "name": "."
    },
    "KC_SLASH": {
      "code": "00 38",
      "name": "/ ?"
    },
    "KC_GRAVE_ACCENT": {
      "code": "00 32",
      "name": "`"
    },
    "KC_MENU": {
      "code": "00 76",
      "name": "Menu"
    },
    "KC_NUM_LOCK": {
      "code": "00 53",
      "name": "Num Lock"
    },
    "KC_KP_SLASH": {
      "code": "00 54",
      "name": "/"
    },
    "KC_KP_ASTERISK": {
      "code": "00 55",
      "name": "*"
    },
    "KC_KP_MINUS": {
      "code": "00 56",
      "name": "-"
    },
    "KC_KP_PLUS": {
      "code": "00 57",
      "name": "+"
    },
    "KC_KP_ENTER": {
      "code": "00 58",
      "name": "Num Enter"
    },
    "KC_KP_1": {
      "code": "00 59",
      "name": "1"
    },
    "KC_KP_2": {
      "code": "00 5A",
      "name": "2"
    },
    "KC_KP_3": {
      "code": "00 5B",
      "name": "3"
    },
    "KC_KP_4": {
      "code": "00 5C",
      "name": "4"
    },
    "KC_KP_5": {
      "code": "00 5D",
      "name": "5"
    },
    "KC_KP_6": {
      "code": "00 5E",
      "name": "6"
    },
    "KC_KP_7": {
      "code": "00 5F",
      "name": "7"
    },
    "KC_KP_8": {
      "code": "00 60",
      "name": "8"
    },
    "KC_KP_9": {
      "code": "00 61",
      "name": "9"
    },
    "KC_KP_0": {
      "code": "00 62",
      "name": "0"
    },
    "KC_KP_DOT": {
      "code": "00 63",
      "name": ". Del"
    },
    "KC_KP_COMMA": {
      "code": "00 85",
      "name": ","
    },
    "KC_KP_EQUAL": {
      "code": "00 67",
      "name": "="
    },
    "KC_SHIFT_AND_GRAVE": {
      "code": "02 35",
      "name": "~"
    },
    "KC_SHIFT_AND_1": {
      "code": "02 1E",
      "name": "!"
    },
    "KC_SHIFT_AND_2": {
      "code": "02 1F",
      "name": "@"
    },
    "KC_SHIFT_AND_3": {
      "code": "02 20",
      "name": "#"
    },
    "KC_SHIFT_AND_4": {
      "code": "02 21",
      "name": "$"
    },
    "KC_SHIFT_AND_5": {
      "code": "02 22",
      "name": "%"
    },
    "KC_SHIFT_AND_6": {
      "code": "02 23",
      "name": "^"
    },
    "KC_SHIFT_AND_7": {
      "code": "02 24",
      "name": "&"
    },
    "KC_SHIFT_AND_8": {
      "code": "02 25",
      "name": "*"
    },
    "KC_SHIFT_AND_9": {
      "code": "02 26",
      "name": "("
    },
    "KC_SHIFT_AND_0": {
      "code": "02 27",
      "name": ")"
    },
    "KC_SHIFT_AND_MINUS": {
      "code": "02 2D",
      "name": "_"
    },
    "KC_SHIFT_AND_EQUAL": {
      "code": "00 57",
      "name": "+"
    },
    "KC_SHIFT_AND_LEFT_BRACKET": {
      "code": "02 2F",
      "name": "{"
    },
    "KC_SHIFT_AND_RIGHT_BRACKET": {
      "code": "02 30",
      "name": "}"
    },
    "KC_SHIFT_AND_BACKSLASH": {
      "code": "02 31",
      "name": "|"
    },
    "KC_SHIFT_AND_SEMICOLON": {
      "code": "02 33",
      "name": ":"
    },
    "KC_SHIFT_AND_APOSTROPHE": {
      "code": "02 34",
      "name": "\""
    },
    "KC_SHIFT_AND_COMMA": {
      "code": "02 36",
      "name": "<"
    },
    "KC_SHIFT_AND_PERIOD": {
      "code": "02 37",
      "name": ">"
    },
    "KC_SHIFT_AND_SLASH": {
      "code": "02 38",
      "name": "?"
    },
    "KC_KANA": {
      "code": "00 88",
      "name": "かな"
    },
    "KC_HENK": {
      "code": "00 8A",
      "name": "變換"
    },
    "KC_MHEN": {
      "code": "00 8B",
      "name": "無變換"
    },
    "KC_HAEN": {
      "code": "00 90",
      "name": "한영"
    },
    "KC_HANJ": {
      "code": "00 91",
      "name": "漢字"
    },
    "BL_TOGG": {
      "code": "78 02",
      "name": "BL Toggle"
    },
    // "BL_ON": {
    //   "code": "78 00",
    //   "name": "BL On"
    // },
    // "BL_OFF": {
    //   "code": "78 01",
    //   "name": "BL Off"
    // },
    // "BL_DEC": {
    //   "code": "78 03",
    //   "name": "BL -"
    // },
    // "BL_INC": {
    //   "code": "78 04",
    //   "name": "BL +"
    // },
    // "BL_STEP": {
    //   "code": "78 05",
    //   "name": "BL Cycle"
    // },
    // "BL_BRTG": {
    //   "code": "78 06",
    //   "name": "BR Toggle"
    // },
    "RGB_TOG": {
      "code": "78 20",
      "name": "RGB Toggle"
    },
    "RGB_RMOD": {
      "code": "78 22",
      "name": "RGB Mode -"
    },
    "RGB_MOD": {
      "code": "78 21",
      "name": "RGB Mode +"
    },
    // "RGB_HUD": {
    //   "code": "78 24",
    //   "name": "Hue -"
    // },
    "RGB_HUI": {
      "code": "78 23",
      "name": "Hue +"
    },
    // "RGB_SAD": {
    //   "code": "78 26",
    //   "name": "Sat -"
    // },
    // "RGB_SAI": {
    //   "code": "78 25",
    //   "name": "Sat +"
    // },
    "RGB_VAD": {
      "code": "78 28",
      "name": "Bright -"
    },
    "RGB_VAI": {
      "code": "78 27",
      "name": "Bright +"
    },
    "RGB_SPD": {
      "code": "78 2A",
      "name": "Effect Speed -"
    },
    "RGB_SPI": {
      "code": "78 29",
      "name": "Effect Speed +"
    },
    // "RGB_M_P": {
    //   "code": "78 2B",
    //   "name": "RGB Mode P"
    // },
    // "RGB_M_B": {
    //   "code": "78 2C",
    //   "name": "RGB Mode B"
    // },
    // "RGB_M_R": {
    //   "code": "78 2D",
    //   "name": "RGB Mode R"
    // },
    // "RGB_M_SW": {
    //   "code": "78 2E",
    //   "name": "RGB Mode SW"
    // },
    // "RGB_M_SN": {
    //   "code": "78 2F",
    //   "name": "RGB Mode SN"
    // },
    // "RGB_M_K": {
    //   "code": "78 30",
    //   "name": "RGB Mode K"
    // },
    // "RGB_M_X": {
    //   "code": "78 31",
    //   "name": "RGB Mode X"
    // },
    // "RGB_M_G": {
    //   "code": "78 32",
    //   "name": "RGB Mode G"
    // },
    "KC_VOLD": {
      "code": "00 AA",
      "name": "Vol -"
    },
    "KC_VOLU": {
      "code": "00 A9",
      "name": "Vol +"
    },
    "KC_MUTE": {
      "code": "00 A8",
      "name": "Mute"
    },
    "KC_MPLY": {
      "code": "00 AE",
      "name": "Play/Pause"
    },
    "KC_MSTP": {
      "code": "00 AD",
      "name": "Stop"
    },
    "KC_MPRV": {
      "code": "00 AC",
      "name": "Previous"
    },
    "KC_MNXT": {
      "code": "00 AB",
      "name": "Next"
    },
    // "KC_MRWD": {
    //   "code": "00 BC",
    //   "name": "Rewind"
    // },
    // "KC_MFFD": {
    //   "code": "00 BB",
    //   "name": "Fast Forward"
    // },
    // "KC_MSEL": {
    //   "code": "00 AF",
    //   "name": "Select"
    // },
    "KC_EJCT": {
      "code": "00 B0",
      "name": "Eject"
    },
    // "KC_LCAP": {
    //   "code": "00 82",
    //   "name": "Locking Caps"
    // },
    // "KC_LNUM": {
    //   "code": "00 83",
    //   "name": "Locking Num"
    // },
    // "KC_LSCR": {
    //   "code": "00 84",
    //   "name": "Locking Scroll"
    // },
    "KC_GESC": {
      "code": "7C 16",
      "name": "Esc ~"
    },
    "RESET": {
      "code": "7C 00",
      "name": "Reset"
    },
    // "DEBUG": {
    //   "code": "7C 02",
    //   "name": "Debug"
    // },
    "MAGIC_TOGGLE_NKRO": {
      "code": "70 13",
      "name": "Toggle NKRO"
    },
    // "KC_ERAS": {
    //   "code": "00 99",
    //   "name": "Alt Erase"
    // },
    // "KC_MS_UP": {
    //   "code": "00 CD",
    //   "name": "Mouse Up"
    // },
    // "KC_MS_DOWN": {
    //   "code": "00 CE",
    //   "name": "Mouse Down"
    // },
    // "KC_MS_LEFT": {
    //   "code": "00 CF",
    //   "name": "Mouse Left"
    // },
    // "KC_MS_RIGHT": {
    //   "code": "00 D0",
    //   "name": "Mouse Right"
    // },
    "KC_MS_BTN1": {
      "code": "00 D1",
      "name": "Mouse 1"
    },
    "KC_MS_BTN2": {
      "code": "00 D2",
      "name": "Mouse 2"
    },
    "KC_MS_BTN3": {
      "code": "00 D3",
      "name": "Mouse 3"
    },
    "KC_MS_BTN4": {
      "code": "00 D4",
      "name": "Mouse 4"
    },
    "KC_MS_BTN5": {
      "code": "00 D5",
      "name": "Mouse 5"
    },
    // "KC_MS_BTN6": {
    //   "code": "00 D6",
    //   "name": "Mouse 6"
    // },
    // "KC_MS_BTN7": {
    //   "code": "00 D7",
    //   "name": "Mouse 7"
    // },
    // "KC_MS_BTN8": {
    //   "code": "00 D8",
    //   "name": "Mouse 8"
    // },
    "KC_MS_WH_UP": {
      "code": "00 D9",
      "name": "Mouse Wheel Up"
    },
    "KC_MS_WH_DOWN": {
      "code": "00 DA",
      "name": "Mouse Wheel Down"
    },
    "KC_MS_WH_LEFT": {
      "code": "00 DB",
      "name": "Mouse Wheel Left"
    },
    "KC_MS_WH_RIGHT": {
      "code": "00 DC",
      "name": "Mouse Wheel Right"
    },
    // "KC_MS_ACCEL0": {
    //   "code": "00 DD",
    //   "name": "Mouse Accel 0"
    // },
    // "KC_MS_ACCEL1": {
    //   "code": "00 DE",
    //   "name": "Mouse Accel 1"
    // },
    // "KC_MS_ACCEL2": {
    //   "code": "00 DF",
    //   "name": "Mouse Accel 2"
    // },
    "SK_TRI_LAYER_LOWER": {
      "code": "7C 77",
      "name": "Fn1(3)"
    },
    "SK_TRI_LAYER_UPPER": {
      "code": "7C 78",
      "name": "Fn2(3)"
    },
    // "SPACE_FN_1": {
    //   "code": "41 2C",
    //   "name": "Space Fn1"
    // },
    // "SPACE_FN_2": {
    //   "code": "42 2C",
    //   "name": "Space Fn2"
    // },
    // "SPACE_FN_3": {
    //   "code": "43 2C",
    //   "name": "Space Fn3"
    // },
    "KR_MOMENTARY_0": {
      "code": "52 20",
      "name": "MO(0)"
    },
    "KR_MOMENTARY_1": {
      "code": "52 21",
      "name": "MO(1)"
    },
    // "KR_TOGGLE_LAYER_0": {
    //   "code": "52 60",
    //   "name": "TO(0)"
    // },
    // "KR_TOGGLE_LAYER_1": {
    //   "code": "52 61",
    //   "name": "TO(1)"
    // },
    // "KR_LAYER_TAP_TOGGLE_0": {
    //   "code": "52 C0",
    //   "name": "TT(0)"
    // },
    // "KR_LAYER_TAP_TOGGLE_1": {
    //   "code": "52 C1",
    //   "name": "TT(1)"
    // },
    // "KR_ONE_SHOT_LAYER_0": {
    //   "code": "52 80",
    //   "name": "OSL(0)"
    // },
    // "KR_ONE_SHOT_LAYER_1": {
    //   "code": "52 81",
    //   "name": "OSL(1)"
    // },
    // "KR_TO_0": {
    //   "code": "52 00",
    //   "name": "TO(0)"
    // },
    // "KR_TO_1": {
    //   "code": "52 01",
    //   "name": "TO(1)"
    // },
    // "KR_DEF_LAYER_0": {
    //   "code": "52 40",
    //   "name": "DF(0)"
    // },
    // "KR_DEF_LAYER_1": {
    //   "code": "52 41",
    //   "name": "DF(1)"
    // },
    "Mac/Win": {
      "code": "7E 02",
      "name": "Mac/Win"
    },
    "Win Lock": {
      "code": "7E 03",
      "name": "Win Lock"
    },
    "Reset": {
      "code": "7C 03",
      "name": "Reset"
    },
    "KC_A1": {
      "code": "7E 05",
      "name": "A1"
    },
    "KC_A2": {
      "code": "7E 06",
      "name": "A2"
    },
    "KC_A3": {
      "code": "7E 07",
      "name": "A3"
    },
    "KC_SOCD_SWITCH": {
      "code": "7E 00",
      "name": "SDT"
    },
    "KC_MYCM": {
      "code": "00 B3",
      "name": "MYCM"
    },
    "KC_PWR": {
      "code": "00 A5",
      "name": "KC_PWR"
    },
    "KC_POWER": {
      "code": "00 66",
      "name": "KC_POWER"
    },
    "KC_SLEP": {
      "code": "00 A6",
      "name": "KC_SLEP"
    },
    "KC_WAKE": {
      "code": "00 A7",
      "name": "KC_WAKE"
    },
    "KC_WWW_SEARCH": {
      "code": "00 B4",
      "name": "KC_WWW_SEARCH"
    },
    "KC_CALC": {
      "code": "00 B2",
      "name": "KC_CALC"
    },
    "KC_MAIL": {
      "code": "00 B1",
      "name": "KC_MAIL"
    },
    "KC_TRNS": {
      "code": "00 01",
      "name": "KC_TRNS"
    }
  }
export default KeyCodeMap;