import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spin } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { useKeyboard } from '../Keyboard/KeyboardContext';
import { useState, useEffect } from 'react';
import { changeToHex } from '../../utils/hidUtils';
import NewAdvancedKeyType from './NewAdvancedKeyType';
import SocdItem from './SOCD/SocdItem';
import SocdConfig from './SOCD/SocdConfig';
import RsItem from './RS/RsItem';
import RsConfig from './RS/RsConfig';
import MtItem from './MT/MtItem';
import MtConfig from './MT/MtConfig';
import DksItem from './DKS/DksItem';
import DksConfig from './DKS/DksConfig';
import TglItem from './TGL/TglItem';
import TglConfig from './TGL/TglConfig';
import { useTranslation } from 'react-i18next';

const AdvancedKey = () => {
  const { advancedKey, setAdvancedKey, addToQueue, showAdvancedKeyTooltip, setShowAdvancedKeyTooltip, contentLoading, deviceProductId } = useHandleDevice();
  const { data, setMaxSelection, setCurrentSelectedKeycaps, updateKeycap } = useKeyboard();
  const [loading, setLoading] = useState(true);
  const [listLoading, setListLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [currentAdvancedKeyType, setCurrentAdvancedKeyType] = useState('');
  const { t } = useTranslation();
  const newAdvancedKeyItem = () => {
    setLoading(false)
    setCurrentStep(1)
    setCurrentSelectedKeycaps([])
    initAdvancedKey('rs')
  };

  const initAdvancedKey = (type) => {
    setCurrentAdvancedKeyType(type)
    setAdvancedKey(prev => {
      const newRsList = prev.rs.list.filter(item =>
        item.keycap1_row || item.keycap1_col || item.keycap2_row || item.keycap2_col
      );

      const newSocdList = prev.socd.list.filter(item =>
        item.keycap1_row || item.keycap1_col || item.keycap2_row || item.keycap2_col
      );

      const newMtList = prev.mt.list.filter(item =>
        item.keycap_row || item.keycap_col ||
        item.short_press_code || item.long_press_code
      );

      const newDksList = prev.dks.list.filter(item =>
        item.keycap_row || item.keycap_col ||
        item.keycap1_code || item.keycap1_config ||
        item.keycap2_code || item.keycap2_config ||
        item.keycap3_code || item.keycap3_config ||
        item.keycap4_code || item.keycap4_config
      );

      const newTglList = prev.tgl.list.filter(item =>
        item.keycap_row || item.keycap_col || item.keycode
      );
      if (type === 'rs') {
        return {
          ...prev,
          rs: {
            ...prev.rs,
            list: [{
              keycap1_row: 0,
              keycap1_col: 0,
              keycap2_row: 0,
              keycap2_col: 0,
            }, ...newRsList],
          },
          socd: {
            ...prev.socd,
            list: newSocdList
          },
          mt: {
            ...prev.mt,
            list: newMtList
          },
          dks: {
            ...prev.dks,
            list: newDksList
          },
          tgl: {
            ...prev.tgl,
            list: newTglList
          }
        }
      } else if (type === 'socd') {
        return {
          ...prev,
          rs: {
            ...prev.rs,
            list: newRsList
          },
          socd: {
            ...prev.socd,
            list: [{
              keycap1_row: 0,
              keycap1_col: 0,
              keycap2_row: 0,
              keycap2_col: 0,
              mode: "00"
            }, ...newSocdList],
          },
          mt: {
            ...prev.mt,
            list: newMtList
          },
          dks: {
            ...prev.dks,
            list: newDksList
          },
          tgl: {
            ...prev.tgl,
            list: newTglList
          }
        };
      } else if (type === 'mt') {
        return {
          ...prev,
          rs: {
            ...prev.rs,
            list: newRsList
          },
          mt: {
            ...prev.mt,
            list: [{
              keycap_row: 0,
              keycap_col: 0,
              long_press_time: "00 C8",
              short_press_code: 0,
              long_press_code: 0
            }, ...newMtList],
          },
          socd: {
            ...prev.socd,
            list: newSocdList
          },
          dks: {
            ...prev.dks,
            list: newDksList
          },
          tgl: {
            ...prev.tgl,
            list: newTglList
          }
        };
      } else if (type === 'dks') {
        return {
          ...prev,
          rs: {
            ...prev.rs,
            list: newRsList
          },
          socd: {
            ...prev.socd,
            list: newSocdList
          },
          mt: {
            ...prev.mt,
            list: newMtList
          },
          dks: {
            ...prev.dks,
            list: [{
              keycap_row: 0,
              keycap_col: 0,
              press_trigger_point: "00 64",
              release_trigger_point: "01 68",
              keycap1_code: 0,
              keycap1_config: 0,
              keycap2_code: 0,
              keycap2_config: 0,
              keycap3_code: 0,
              keycap3_config: 0,
              keycap4_code: 0,
              keycap4_config: 0,
            }, ...newDksList],
          },
          tgl: {
            ...prev.tgl,
            list: newTglList
          }
        };
      } else if (type === 'tgl') {
        return {
          ...prev,
          rs: {
            ...prev.rs,
            list: newRsList
          },
          mt: {
            ...prev.mt,
            list: newMtList
          },
          socd: {
            ...prev.socd,
            list: newSocdList
          },
          dks: {
            ...prev.dks,
            list: newDksList
          },
          tgl: {
            ...prev.tgl,
            list: [{
              keycap_row: 0,
              keycap_col: 0,
              keycode: 0
            }, ...newTglList],
          }
        };
      }
      return prev;
    });
  }

  const handleSelectAdvancedKeyType = (type) => {
    setCurrentAdvancedKeyType(type)
    initAdvancedKey(type)
  }

  const handleSelectAdvancedKeyItem = (type, index) => {
    const newSelectedIndex = (advancedKey.selectedIndex === index && advancedKey.advancedKeyType === type) ? null : index;

    const updateAllKeycaps = () => {
      advancedKey.rs.list.forEach((item) => {
        updateKeycap(item.keycap1_row, item.keycap1_col, { advancedKeyType: 'rs' })
        updateKeycap(item.keycap2_row, item.keycap2_col, { advancedKeyType: 'rs' })
      })
      advancedKey.socd.list.forEach((item) => {
        updateKeycap(item.keycap1_row, item.keycap1_col, { advancedKeyType: 'socd' })
        updateKeycap(item.keycap2_row, item.keycap2_col, { advancedKeyType: 'socd' })
      })
      advancedKey.mt.list.forEach((item) => {
        updateKeycap(item.keycap_row, item.keycap_col, { advancedKeyType: 'mt' })
      })
      advancedKey.dks.list.forEach((item) => {
        updateKeycap(item.keycap_row, item.keycap_col, { advancedKeyType: 'dks' })
      })
      advancedKey.tgl.list.forEach((item) => {
        updateKeycap(item.keycap_row, item.keycap_col, { advancedKeyType: 'tgl' })
      })
    }

    const getKeycapInfo = (list, index, type) => {
      if (['rs', 'socd'].includes(type)) {
        return [{
          row: list[index].keycap1_row,
          column: list[index].keycap1_col,
          label: data.keycaps[data.currentLayer]?.[`${list[index].keycap1_row}-${list[index].keycap1_col}`]?.label
        }, {
          row: list[index].keycap2_row,
          column: list[index].keycap2_col,
          label: data.keycaps[data.currentLayer]?.[`${list[index].keycap2_row}-${list[index].keycap2_col}`]?.label
        }]
      } else {
        return [{
          row: list[index].keycap_row,
          column: list[index].keycap_col,
          label: data.keycaps[data.currentLayer]?.[`${list[index].keycap_row}-${list[index].keycap_col}`]?.label
        }]
      }
    }


    return () => {
      if (newSelectedIndex !== null) {
        setLoading(false)
        setCurrentStep(2)
      } else {
        setLoading(true)
        setCurrentStep(1)
      }

      setCurrentAdvancedKeyType(type)
      setAdvancedKey(prev => ({
        ...prev,
        advancedKeyType: type
      }))

      setMaxSelection(['rs', 'socd'].includes(type) ? 2 : 1)
      setAdvancedKey(prev => ({
        ...prev,
        [type]: {
          ...prev[type],
        },
        selectedIndex: newSelectedIndex
      }));

      setCurrentSelectedKeycaps([])
      updateAllKeycaps()

      if (newSelectedIndex !== null) {
        const list = advancedKey[type].list
        const keycapInfo = getKeycapInfo(list, index, type)
        setCurrentSelectedKeycaps(keycapInfo)

        if (['rs', 'socd'].includes(type)) {
          updateKeycap(list[index].keycap1_row, list[index].keycap1_col, { advancedKeyType: '' })
          updateKeycap(list[index].keycap2_row, list[index].keycap2_col, { advancedKeyType: '' })
        } else {
          updateKeycap(list[index].keycap_row, list[index].keycap_col, { advancedKeyType: '' })
        }
      }
    }
  }

  // 进入配置页面
  const handleNextStep = () => {
    const maxSelections = {
      'rs': 2,
      'socd': 2,
      'mt': 1,
      'dks': 1,
      'tgl': 1
    };

    setMaxSelection(maxSelections[currentAdvancedKeyType]);

    if (currentStep === 2 && (currentAdvancedKeyType === 'socd' || currentAdvancedKeyType === 'mt')) {
      // socd和mt拆分了步骤不执行
    }
    else{
      setAdvancedKey(prev => ({
        ...prev,
        rs: { ...prev.rs },
        socd: { ...prev.socd },
        mt: { ...prev.mt },
        dks: { ...prev.dks },
        tgl: { ...prev.tgl },
        selectedIndex: ['dks', 'tgl'].includes(currentAdvancedKeyType) && prev.selectedIndex !== null ?
          prev.selectedIndex : 0,
        advancedKeyType: currentAdvancedKeyType
      }));
    }

    setCurrentStep(currentStep + 1);
  }

  // 保存高级键
  const saveAdvancedKey = () => {
    setListLoading(true)
    if (currentAdvancedKeyType === 'rs') {
      addToQueue(`2F ${changeToHex(advancedKey.rs.list.length)}`);
      advancedKey.rs.list.forEach((item, index) => {
        addToQueue(`31 ${changeToHex(index)} ${item.keycap1_row} ${item.keycap1_col} ${item.keycap2_row} ${item.keycap2_col}`)
      });
      advancedKey.rs.list.forEach((item, index) => {
        updateKeycap(item.keycap1_row, item.keycap1_col, { advancedKeyType: 'rs' })
        updateKeycap(item.keycap2_row, item.keycap2_col, { advancedKeyType: 'rs' })
      })
    } else if (currentAdvancedKeyType === 'socd') {
      addToQueue(`1F ${changeToHex(advancedKey.socd.list.length)}`);
      advancedKey.socd.list.forEach((item, index) => {
        addToQueue(`21 ${changeToHex(index)} ${item.keycap1_row} ${item.keycap1_col} ${item.keycap2_row} ${item.keycap2_col} ${item.mode}`)
      });
      advancedKey.socd.list.forEach((item, index) => {
        updateKeycap(item.keycap1_row, item.keycap1_col, { advancedKeyType: 'socd' })
        updateKeycap(item.keycap2_row, item.keycap2_col, { advancedKeyType: 'socd' })
      })
    } else if (currentAdvancedKeyType === 'mt') {
      addToQueue(`23 ${changeToHex(advancedKey.mt.list.length)}`);
      advancedKey.mt.list.forEach((item, index) => {
        addToQueue(`25 ${changeToHex(index)} ${item.keycap_row} ${item.keycap_col} ${item.long_press_time} ${item.short_press_code} ${item.long_press_code}`)
      });

      advancedKey.mt.list.forEach((item, index) => {
        updateKeycap(item.keycap_row, item.keycap_col, { advancedKeyType: 'mt' })
      })
    } else if (currentAdvancedKeyType === 'dks') {
      addToQueue(`2B ${changeToHex(advancedKey.dks.list.length)}`);
      advancedKey.dks.list.forEach((item, index) => {
        addToQueue(`2D ${changeToHex(index)} ${item.keycap_row} ${item.keycap_col} ${item.press_trigger_point}
          ${item.release_trigger_point} ${item.keycap1_code || "00 00"} ${item.keycap1_config} ${item.keycap2_code || "00 00"}
          ${item.keycap2_config} ${item.keycap3_code || "00 00"} ${item.keycap3_config} ${item.keycap4_code || "00 00"}
          ${item.keycap4_config}`)
      })

      advancedKey.dks.list.forEach((item, index) => {
        updateKeycap(item.keycap_row, item.keycap_col, { advancedKeyType: 'dks' })
      })
    } else if (currentAdvancedKeyType === 'tgl') {
      addToQueue(`27 ${changeToHex(advancedKey.tgl.list.length)}`);
      advancedKey.tgl.list.forEach((item, index) => {
        addToQueue(`29 ${changeToHex(index)} ${item.keycap_row} ${item.keycap_col} ${item.keycode}`)
      })

      advancedKey.tgl.list.forEach((item, index) => {
        updateKeycap(item.keycap_row, item.keycap_col, { advancedKeyType: 'tgl' })
      })
    }
    setAdvancedKey(prev => ({
      ...prev,
      selectedIndex: null
    }));
    setCurrentSelectedKeycaps([]);
    setLoading(true)
    setCurrentStep(0)
    setTimeout(() => {
      setListLoading(false)
    }, 200)
  }

  // 取消配置内容
  const handleCancel = () => {
    setListLoading(true)
    setMaxSelection(0)
    setCurrentAdvancedKeyType('') //这个地方要更新下
    setLoading(true)
    reGetAdvancedKey()
    setCurrentStep(0)
    setTimeout(() => {
      setListLoading(false)
    }, 200)
  }

  // 删除高级键
  const deleteAdvancedKeyItem = (type,index) => {
    return () => {
      setCurrentSelectedKeycaps([])
      setLoading(true)
      setCurrentStep(0)
      if (type === 'rs') {
        setAdvancedKey(prev => {
          const deletedItem = prev.rs.list[index];
          // Clear advancedKeyType for the deleted combination
          updateKeycap(deletedItem.keycap1_row, deletedItem.keycap1_col, { advancedKeyType: '' });
          updateKeycap(deletedItem.keycap2_row, deletedItem.keycap2_col, { advancedKeyType: '' });

          const newList = prev.rs.list.filter((_, i) => i !== index);

          addToQueue(`2F ${changeToHex(newList.length)}`);
          newList.forEach((item, idx) => {
            addToQueue(`31 ${changeToHex(idx)} ${item.keycap1_row} ${item.keycap1_col} ${item.keycap2_row} ${item.keycap2_col}`)
          });

          return {
            ...prev,
            rs: {
              ...prev.rs,
              list: newList
            },
            selectedIndex: null
          };
        });
      } else if (type === 'socd') {
        setAdvancedKey(prev => {
          const deletedItem = prev.socd.list[index];
          // Clear advancedKeyType for the deleted combination
          updateKeycap(deletedItem.keycap1_row, deletedItem.keycap1_col, { advancedKeyType: '' });
          updateKeycap(deletedItem.keycap2_row, deletedItem.keycap2_col, { advancedKeyType: '' });

          const newList = prev.socd.list.filter((_, i) => i !== index);

          addToQueue(`1F ${changeToHex(newList.length)}`);
          newList.forEach((item, idx) => {
            addToQueue(`21 ${changeToHex(idx)} ${item.keycap1_row} ${item.keycap1_col} ${item.keycap2_row} ${item.keycap2_col} ${item.mode}`)
          });

          return {
            ...prev,
            socd: {
              ...prev.socd,
              list: newList
            },
            selectedIndex: null
          };
        });
      } else if (type === 'mt') {
        setAdvancedKey(prev => {
          const deletedItem = prev.mt.list[index];
          updateKeycap(deletedItem.keycap_row, deletedItem.keycap_col, { advancedKeyType: '' });
          const newList = prev.mt.list.filter((_, i) => i !== index);
          addToQueue(`23 ${changeToHex(newList.length)}`);
          newList.forEach((item, idx) => {
            addToQueue(`25 ${changeToHex(idx)} ${item.keycap_row} ${item.keycap_col} ${item.long_press_time} ${item.short_press_code} ${item.long_press_code}`)
          });
          return {
            ...prev,
            mt: {
              ...prev.mt,
              list: newList
            }
          };
        });
      } else if (type === 'dks') {
        setAdvancedKey(prev => {
          const deletedItem = prev.dks.list[index];
          updateKeycap(deletedItem.keycap_row, deletedItem.keycap_col, { advancedKeyType: '' });
          const newList = prev.dks.list.filter((_, i) => i !== index);
          addToQueue(`2B ${changeToHex(newList.length)}`);
          newList.forEach((item, idx) => {
            addToQueue(`2D ${changeToHex(idx)} ${item.keycap_row} ${item.keycap_col} ${item.press_trigger_point} ${item.release_trigger_point} ${item.keycap1_code} ${item.keycap1_config} ${item.keycap2_code} ${item.keycap2_config} ${item.keycap3_code} ${item.keycap3_config} ${item.keycap4_code} ${item.keycap4_config} 00 00 00 00 00 00 00 00 00 00 00 00`)
          });
          return {
            ...prev,
            dks: {
              ...prev.dks,
              list: newList
            }
          };
        });
      } else if (type === 'tgl') {
        setAdvancedKey(prev => {
          const deletedItem = prev.tgl.list[index];
          updateKeycap(deletedItem.keycap_row, deletedItem.keycap_col, { advancedKeyType: '' });
          const newList = prev.tgl.list.filter((_, i) => i !== index);
          addToQueue(`27 ${changeToHex(newList.length)}`);
          newList.forEach((item, idx) => {
            addToQueue(`29 ${changeToHex(idx)} ${item.keycap_row} ${item.keycap_col} ${item.keycode}`)
          });
          return {
            ...prev,
            tgl: {
              ...prev.tgl,
              list: newList
            }
          };
        });
      }
    };
  };

  const reGetAdvancedKey = () => {
    resetAdvancedKey()
    // 获取RS
    addToQueue("2E")
    // 获取SOCD
    addToQueue("1E")
    // 获取MT
    addToQueue("22")
    // 获取DKS
    addToQueue("2A")
    // 获取TGL
    addToQueue("26")
  }

  const resetAdvancedKey = () => {
    setCurrentSelectedKeycaps([])
    setAdvancedKey(prev => ({
      ...prev,
      selectedIndex: null,
      rs: {
        ...prev.rs,
        list: prev.rs.list.slice(1, prev.rs.list.length)
      },
      socd: {
        ...prev.socd,
        list: prev.socd.list.slice(1, prev.socd.list.length)
      },
      mt: {
        ...prev.mt,
        list: prev.mt.list.slice(1, prev.mt.list.length)
      },
      dks: {
        ...prev.dks,
        list: prev.dks.list.slice(1, prev.dks.list.length)
      },
      tgl: {
        ...prev.tgl,
        list: prev.tgl.list.slice(1, prev.tgl.list.length)
      }
    }))

    Object.keys(data.keycaps['00']).forEach(key => {
      const [row, col] = key.split('-');
      updateKeycap(row, col, { advancedKeyType: '' }, '00');
    });

    Object.keys(data.keycaps['01']).forEach(key => {
      const [row, col] = key.split('-');
      updateKeycap(row, col, { advancedKeyType: '' }, '01');
    });
  }

  useEffect(() => {
    const hasAdvancedKeys = advancedKey.socd.list.length > 0 ||
                           advancedKey.mt.list.length > 0 ||
                           advancedKey.dks.list.length > 0 ||
                           advancedKey.rs.list.length > 0 ||
                           advancedKey.tgl.list.length > 0;

    let timeoutId;
    if (!hasAdvancedKeys && !contentLoading) {
      timeoutId = setTimeout(() => {
        const currentHasAdvancedKeys = advancedKey.socd.list.length > 0 ||
                                     advancedKey.mt.list.length > 0 ||
                                     advancedKey.dks.list.length > 0 ||
                                     advancedKey.rs.list.length > 0 ||
                                     advancedKey.tgl.list.length > 0;
        if (!currentHasAdvancedKeys) {
          setShowAdvancedKeyTooltip(true);
        }
      }, 1000);
    } else {
      setShowAdvancedKeyTooltip(false);
    }

    // Cleanup function to clear timeout if component unmounts or dependencies change
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [advancedKey.socd.list, advancedKey.mt.list, advancedKey.dks.list]);

  return(
    <div style={{display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', marginTop: '2em'}}>
      <div className="d-flex">
        {/* 这个应该是个左边的栏目 */}
        {
               [0].includes(currentStep) && <>
         <div>
          <div className="d-flex align-items-center" style={{justifyContent: 'space-between', marginRight: '1em'}}>
            <div className="d-flex">
              <div style={{height: '1.5em', width: '0.2em', borderRadius: '0.25em', background: '#1668dc', marginRight: '0.5em'}}></div>
              <div style={{fontSize: '1.2em', fontWeight: 'bold'}}>{t('advanced_key.advanced_key_management_list')}</div>
            </div>
            <div>
              <Tooltip
                placement="topLeft"
                title={t('advanced_key.new_advanced_key_tip')}
                color="#3974FF"
                arrow={true}
                open={showAdvancedKeyTooltip}
              >
                <Button icon={<PlusOutlined />} onClick={newAdvancedKeyItem}>{t('advanced_key.new_advanced_key')}</Button>
              </Tooltip>
            </div>
          </div>
          <div
            style={{
              display: 'block',
              width: '542px',
              borderRadius: '1em',
              marginRight: '1em',
              height: '470px',
            }}
            className='custom-card-container'
          >
            <div className="custom-card-inside-container">
              <Spin spinning={listLoading} style={{height: '200px', width: '100%'}}>
                {!listLoading && <div>
                  {advancedKey.rs.list.map((item, index) => (
                    <RsItem
                      key={index}
                      item={item}
                      isActive={advancedKey.advancedKeyType === 'rs' && advancedKey.selectedIndex === index}
                      onSelect={(e) => {
                        // 如果点击来自删除按钮，不触发选中事件
                        if (!e.target.closest('button')) {
                          handleSelectAdvancedKeyItem('rs', index)();

                        }
                      }}
                      onDelete={(e) => {
                        e.stopPropagation(); // 阻止事件冒泡到onSelect
                        deleteAdvancedKeyItem('rs', index)();
                      }}
                    />
                  ))}
                  {advancedKey.socd.list.map((item, index) => (
                    <SocdItem
                      key={index}
                      item={item}
                      isActive={advancedKey.advancedKeyType === 'socd' && advancedKey.selectedIndex === index}
                      onSelect={(e) => {
                        // 如果点击来自删除按钮，不触发选中事件
                        if (!e.target.closest('button')) {
                          handleSelectAdvancedKeyItem('socd', index)();
                        }
                      }}
                      onDelete={(e) => {
                        e.stopPropagation(); // 阻止事件冒泡到onSelect
                        deleteAdvancedKeyItem('socd', index)();
                      }}
                    />
                  ))}

                  {advancedKey.mt.list.map((item, index) => (
                    <MtItem
                      key={index}
                      item={item}
                      isActive={advancedKey.advancedKeyType === 'mt' && advancedKey.selectedIndex === index}
                      onSelect={(e) => {
                        if (!e.target.closest('button')) {
                          handleSelectAdvancedKeyItem('mt', index)();
                        }
                      }}
                      onDelete={(e) => {
                        e.stopPropagation(); // 阻止事件冒泡到onSelect
                        deleteAdvancedKeyItem('mt', index)();
                      }}
                    />
                  ))}

                  {advancedKey.dks.list.map((item, index) => (
                    <DksItem
                      key={index}
                      item={item}
                      isActive={advancedKey.advancedKeyType === 'dks' && advancedKey.selectedIndex === index}
                      onSelect={(e) => {
                        handleSelectAdvancedKeyItem('dks', index)();
                      }}
                      onDelete={(e) => {
                        e.stopPropagation(); // 阻止事件冒泡到onSelect
                        deleteAdvancedKeyItem('dks', index)();
                      }}
                    />
                  ))}
                  {advancedKey.tgl.list.map((item, index) => (
                    <TglItem
                      key={index}
                      item={item}
                      isActive={advancedKey.advancedKeyType === 'tgl' && advancedKey.selectedIndex === index}
                      onSelect={(e) => {
                        handleSelectAdvancedKeyItem('tgl', index)();
                      }}
                      onDelete={(e) => {
                        e.stopPropagation(); // 阻止事件冒泡到onSelect
                        deleteAdvancedKeyItem('tgl', index)();
                      }}
                    />
                  ))}
                </div>}
              </Spin>
            </div>
          </div>
        </div>
               </>
        }

       {/* 这个是右边的栏目 */}
        <div>
        <div className="d-flex justify-content-between">

             <div className="d-flex align-items-center">
             {
               [1,2,3].includes(currentStep) && <>
              <div style={{height: '1.5em', width: '0.2em', borderRadius: '0.25em', background: '#1668dc', marginRight: '0.5em'}}></div>
              <div style={{fontSize: '1.2em', fontWeight: 'bold'}}>{t('advanced_key.advanced_key_settings')}</div>
              </>
             }
            </div>
            <div style={{height: '32px'}}>
              <Spin indicator={<div></div>} spinning={loading} size="large">
                {!loading && <Button style={{marginRight: '1em'}} onClick={handleCancel}>{t('cancel')}</Button>}
                {!loading && currentStep === 1 && <Button type="primary" className={currentAdvancedKeyType === ''  && 'disable'} onClick={handleNextStep}>{t('next_step')}</Button>}
                {!loading && currentStep === 2 && currentAdvancedKeyType === 'dks' && <Button type="primary" onClick={handleNextStep}>{t('next_step')}</Button>}
                {!loading && currentStep === 3 && currentAdvancedKeyType === 'dks' && <Button type="primary" onClick={saveAdvancedKey}>{t('finish')}</Button>}
                {!loading && currentStep === 2 && currentAdvancedKeyType !== 'dks' && currentAdvancedKeyType !== 'mt' && currentAdvancedKeyType !== 'rs' && currentAdvancedKeyType !== 'socd' && <Button type="primary" onClick={saveAdvancedKey}>{t('finish')}</Button>}
                {!loading && currentStep === 2 && currentAdvancedKeyType === 'rs'  && <Button className={data.currentSelectedKeycaps.length !== 2  && 'disable'} type="primary" onClick={saveAdvancedKey}>{t('finish')}</Button>}
                {!loading && currentStep === 2 && currentAdvancedKeyType === 'socd'  && <Button className={data.currentSelectedKeycaps.length !== 2  && 'disable'} type="primary" onClick={handleNextStep}>{t('next_step')}</Button>}
                {!loading && currentStep === 3 && currentAdvancedKeyType === 'socd'  && <Button type="primary" onClick={saveAdvancedKey}>{t('finish')}</Button>}
                {!loading && currentStep === 2 && currentAdvancedKeyType === 'mt'  && <Button type="primary" onClick={handleNextStep}>{t('next_step')}</Button>}
                {!loading && currentStep === 3 && currentAdvancedKeyType === 'mt'  && <Button type="primary" onClick={saveAdvancedKey}>{t('finish')}</Button>}
              </Spin>
            </div>

          </div>

          <div
            style={{
              width: [0].includes(currentStep) ? '758px' : '1080px',
              overflow: 'auto',
              display: 'block',
              height: '470px',
            }}
            className='custom-card-container'
            onTouchMove={(e) => {
              e.currentTarget.style.overflow = 'scroll';
            }}
          > <div className="custom-card-inside-container">
              {advancedKey.advancedKeyType === 'rs' && advancedKey.selectedIndex !== null && (
                <RsConfig
                  firstKeyRow={advancedKey.rs.list[advancedKey.selectedIndex]?.keycap1_row}
                  firstKeyCol={advancedKey.rs.list[advancedKey.selectedIndex]?.keycap1_col}
                  secondKeyRow={advancedKey.rs.list[advancedKey.selectedIndex]?.keycap2_row}
                  secondKeyCol={advancedKey.rs.list[advancedKey.selectedIndex]?.keycap2_col}
                />
              )}
              {advancedKey.advancedKeyType === 'socd' && advancedKey.selectedIndex !== null && (
                <SocdConfig
                  firstKeyRow={advancedKey.socd.list[advancedKey.selectedIndex]?.keycap1_row}
                  firstKeyCol={advancedKey.socd.list[advancedKey.selectedIndex]?.keycap1_col}
                  secondKeyRow={advancedKey.socd.list[advancedKey.selectedIndex]?.keycap2_row}
                  secondKeyCol={advancedKey.socd.list[advancedKey.selectedIndex]?.keycap2_col}
                  selectedMode={advancedKey.socd.list[advancedKey.selectedIndex]?.mode}
                  stepNumber={currentStep}
                />
              )}
              {advancedKey.advancedKeyType === 'mt' && advancedKey.selectedIndex !== null && (
                <MtConfig
                  keyRow={advancedKey.mt.list[advancedKey.selectedIndex]?.keycap_row}
                  keyCol={advancedKey.mt.list[advancedKey.selectedIndex]?.keycap_col}
                  longPressTime={advancedKey.mt.list[advancedKey.selectedIndex]?.long_press_time}
                  shortPressCode={advancedKey.mt.list[advancedKey.selectedIndex]?.short_press_code}
                  longPressCode={advancedKey.mt.list[advancedKey.selectedIndex]?.long_press_code}
                  currentStep={currentStep}
                />
              )}
              {advancedKey.advancedKeyType === 'dks' && advancedKey.selectedIndex !== null && (
                <DksConfig
                  keyRow={advancedKey.dks.list[advancedKey.selectedIndex]?.keycap1_row}
                  keyCol={advancedKey.dks.list[advancedKey.selectedIndex]?.keycap1_col}
                  pressTriggerPoint={advancedKey.dks.list[advancedKey.selectedIndex]?.press_trigger_point}
                  releaseTriggerPoint={advancedKey.dks.list[advancedKey.selectedIndex]?.release_trigger_point}
                  keycap1Code={advancedKey.dks.list[advancedKey.selectedIndex]?.keycap1_code}
                  keycap1Config={advancedKey.dks.list[advancedKey.selectedIndex]?.keycap1_config}
                  keycap2Code={advancedKey.dks.list[advancedKey.selectedIndex]?.keycap2_code}
                  keycap2Config={advancedKey.dks.list[advancedKey.selectedIndex]?.keycap2_config}
                  keycap3Code={advancedKey.dks.list[advancedKey.selectedIndex]?.keycap3_code}
                  keycap3Config={advancedKey.dks.list[advancedKey.selectedIndex]?.keycap3_config}
                  keycap4Code={advancedKey.dks.list[advancedKey.selectedIndex]?.keycap4_code}
                  keycap4Config={advancedKey.dks.list[advancedKey.selectedIndex]?.keycap4_config}
                  currentStep={currentStep}
                />
              )}
              {advancedKey.advancedKeyType === 'tgl' && advancedKey.selectedIndex !== null && (
                <TglConfig
                  keyRow={advancedKey.tgl.list[advancedKey.selectedIndex]?.keycap_row}
                  keyCol={advancedKey.tgl.list[advancedKey.selectedIndex]?.keycap_col}
                  keycode={advancedKey.tgl.list[advancedKey.selectedIndex]?.keycode}
                  currentStep={currentStep}
                />
              )}
              {advancedKey.selectedIndex === null && (
                <Spin indicator={<div></div>} spinning={loading} size="large">
                  <NewAdvancedKeyType onSelect={handleSelectAdvancedKeyType} currentAdvancedKeyType={currentAdvancedKeyType} deviceProductId={deviceProductId} stepNumber={currentStep} />

                </Spin>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
};

export default AdvancedKey;
