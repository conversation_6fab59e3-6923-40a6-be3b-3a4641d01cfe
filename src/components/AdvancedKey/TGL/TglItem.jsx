import { CloseOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useKeyboard } from '../../Keyboard/KeyboardContext';
import { findNameByCode } from '../../../utils/hidUtils';
import TglIcon from './TglIcon';
import processString from '../../Keyboard/processString' ;

const TglItem = ({ item, isActive, onSelect, onDelete }) => {
  const { data } = useKeyboard();
  const selectedKeycap = data.keycaps[data.currentLayer]?.[`${item.keycap_row}-${item.keycap_col}`];
  const keycode = findNameByCode(`${item.keycode}`);

  return (
    <div className="advanced-key-item-wrapper" onClick={onSelect}>
      <div className={`d-flex justify-content-between align-items-center advanced-key-item ${isActive ? 'active' : ''}`}>
        <div className="d-flex align-items-center">
          <div className="d-flex align-items-center" style={{width: '80px', gap: '8px'}}>
            <div className="keycap1 keycap-item">
               {/* {selectedKeycap?.label || ''} */}
               <svg aria-hidden="true"  width="36"  height="36">
                <use href= {`#icon-${selectedKeycap?.label   ?   processString(selectedKeycap?.label,'picture') : 'evenodd'}`}/>
            </svg>
            </div>
          </div>
          <div className="divider-line"></div>
          <div className="keycap2 keycap-item">
            {/* {keycode || ''} */}
            <svg aria-hidden="true"  width="36"  height="36">
                <use href= {`#icon-${keycode &&  keycode !== 'default' ?   processString(keycode,'picture') : 'NO'}`}/>
            </svg>
          </div>
        </div>
        <div className="d-flex align-items-center">
          <div className="icon">
            <TglIcon color={isActive ? "#1668dc" : "#EFF0F5"} fillOpacity={isActive ? "1" : "0.45"} />
          </div>
          <div style={{width: '36px'}}>
            <Button type="text" icon={<CloseOutlined />} className="delete-button" onClick={onDelete} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TglItem;