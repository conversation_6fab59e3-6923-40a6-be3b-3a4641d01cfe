import keyboardLayout80 from './layouts/ez80.json';
import keyboardLayout63 from './layouts/ez63.json';
import keyboardLayout60 from './layouts/ez60.json';
import keyboardLayout75 from './layouts/ez75.json'
import keyboardLayoutitx68 from './layouts/itx68.json';
import ez63_background from '../../assets/ez63_background.png';
import ez63_2_background from '../../assets/ez63_2_background.png';
import ez80_background from '../../assets/ez80_background.png';
import ez60_background from '../../assets/ez60_background.png';
import ev63_background from '../../assets/ev63_background.png';
import ez75_background from '../../assets/ez75_background.png';

import Keyboard from './Keyboard';

const layouts = [
  { name: 'ez63', version: 'default', pid: null, layout: keyboardLayout63, background: ez63_background, width: "969px", height: "390px", paddingTop: "67px", paddingLeft: "35px"},
  { name: 'ez60', version: 'old', pid: 36880, layout: keyboardLayout60, background: ez60_background, width: "969px", height: "390px", paddingTop: "67px", paddingLeft: "35px"},
  { name: 'ez63', version: 'old', pid: 32773, layout: keyboardLayout63, background: ez63_background, width: "969px", height: "390px", paddingTop: "67px", paddingLeft: "35px"},
  { name: 'ez63', version: 'new', pid: 36869, layout: keyboardLayout63, background: ez63_background, width: "969px", height: "390px", paddingTop: "67px", paddingLeft: "35px"},
  { name: 'ez63', version: 'update', pid: 25344, bootloaderPid: 25345, layout: keyboardLayout63, background: ez63_2_background, width: "969px", height: "390px", paddingTop: "67px", paddingLeft: "35px"},
  { name: 'ev63', version: 'normal', pid: 25569, bootloaderPid: 25570, layout: keyboardLayout63, background: ev63_background, width: "948px", height: "392px", paddingTop: "31px", paddingLeft: "25px"},
  { name: 'ez80', version: 'normal', pid: 9010, bootloaderPid: 4660, layout: keyboardLayout80, background: ez80_background, width: "1168px", height: "460px", paddingTop: "56px", paddingLeft: "35px"},
  { name: 'itx68', version: 'normal', pid: 26624, bootloaderPid: 26625, layout: keyboardLayoutitx68, background: ez63_2_background, width: "1034px", height: "390px", paddingTop: "64px", paddingLeft: "38px"},
  { name: 'ez75', version: 'normal', pid: 29952, bootloaderPid: 29953, layout: keyboardLayout75, background: ez75_background, width: "1030px", height: "460px", paddingTop: "55px", paddingLeft: "34px"},
  { name: 'ec63', version: 'normal', pid: 25346, bootloaderPid: 25347, layout: keyboardLayout63, background: ez63_2_background, width: "969px", height: "390px", paddingTop: "67px", paddingLeft: "35px"},
];

// 新增方法：通过pid输出layout_json
export function getLayoutJsonByPid(pid, get_data=false) {
  if (get_data) {
    if (pid === 36880 || pid === 36869 || pid === 32773) {
      return keyboardLayout80;
    }
  }
  const layoutObj = layouts.find(layout => layout.pid === pid || layout.bootloaderPid === pid);
  return layoutObj ? layoutObj.layout : null;
}

export const HandleLayout = ({ pid, showBackground = true, style = {} }) => {
  const layout = layouts.find(layout => layout.pid === pid || layout.bootloaderPid === pid);

  return (
    <div style={style}>
      <Keyboard layout={layout.layout} background={showBackground ? layout.background : null} width={layout.width} height={layout.height} paddingTop={layout.paddingTop} paddingLeft={layout.paddingLeft} />
    </div>
  )
}

// export default HandleLayout;