import './Keytest.css'

import React, { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import KeyboardTest from './KeyboardTest.jsx';
import KeyTestLogs from './KeyTestLogs.jsx';
import GetKeyVoltage from './GetKeyVoltage';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { useKeyboard } from '../Keyboard/KeyboardContext.jsx';
import { Drawer, Table, Button, Modal, Form, InputNumber, Space, Popconfirm, message, Select, Input, Descriptions } from 'antd';
import { getPositionByNumber } from '../../utils/hidUtils';

const defaultConfig = {
  averageVoltages: 0,
  bottomAverageVoltages: 0,
  name: '',
  topPositiveOffset: 0,
  topNegativeOffset: 0,
  bottomPositiveOffset: 0,
  bottomNegativeOffset: 0,
  triggerTravel: 0,
};

const LOCAL_STORAGE_KEY = 'keytest_configs';
const LOCAL_STORAGE_SELECTED_KEY = 'keytest_selected_config_key';

// 管理员密码（可根据需要更改）
const ADMIN_PASSWORD = '123123';

const loadConfigsFromStorage = () => {
  try {
    const configs = JSON.parse(localStorage.getItem(LOCAL_STORAGE_KEY));
    if (Array.isArray(configs) && configs.length > 0) {
      // 兼容老数据：如果没有 name/averageVoltages/bottomAverageVoltages 字段，补充默认
      return configs.map((cfg, idx) => ({
        averageVoltages: typeof cfg.averageVoltages === 'number' ? cfg.averageVoltages : 0,
        bottomAverageVoltages: typeof cfg.bottomAverageVoltages === 'number' ? cfg.bottomAverageVoltages : 0,
        ...cfg,
        name: typeof cfg.name === 'string' ? cfg.name : `配置${cfg.key || idx + 1}`
      }));
    }
  } catch (e) {}
  return [{ ...defaultConfig, key: 1, name: '配置1' }];
};

const loadSelectedConfigKeyFromStorage = (configs) => {
  try {
    const key = JSON.parse(localStorage.getItem(LOCAL_STORAGE_SELECTED_KEY));
    if (configs.some(cfg => cfg.key === key)) {
      return key;
    }
  } catch (e) {}
  return configs[0]?.key || 1;
};

const saveConfigsToStorage = (configs) => {
  localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(configs));
};

const saveSelectedConfigKeyToStorage = (key) => {
  localStorage.setItem(LOCAL_STORAGE_SELECTED_KEY, JSON.stringify(key));
};

const getNextConfigKey = (configs) => {
  // Find the max key and add 1
  if (!configs.length) return 1;
  return Math.max(...configs.map(cfg => Number(cfg.key) || 0)) + 1;
};

const KeyTest = ({ deviceProductId }) => {
  const { t } = useTranslation();
  const [flag, setFlag] = useState('logs'); //控制按钮
  const {
    dataQueue,
    addToQueue,
    setAdjustStatus,
    setAdjustTimes,
    setNeedAdjustKeys,
    firmwareVersion,
    averageVoltages,
    setAverageVoltages,
    bottomAverageVoltages,
    setBottomAverageVoltages,
    topNegativeOffset,
    setTopNegativeOffset,
    topPositiveOffset,
    setTopPositiveOffset,
    bottomNegativeOffset,
    setBottomNegativeOffset,
    bottomPositiveOffset,
    setBottomPositiveOffset,
    keyTriggerStroke,
    setKeyTriggerStroke,
  } = useHandleDevice();
  const [tmpMeedAdjustKeys, setTmpMeedAdjustKeys] = useState([]);
  const { data, updateKeycap } = useKeyboard();

  // 管理员配置相关
  const [configs, setConfigs] = useState(() => loadConfigsFromStorage());
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editConfig, setEditConfig] = useState(null);
  const [form] = Form.useForm();
  // configKey is always the next available key
  const [configKey, setConfigKey] = useState(() => getNextConfigKey(loadConfigsFromStorage()));

  // 新增：当前选中的配置
  const [selectedConfigKey, setSelectedConfigKey] = useState(() => loadSelectedConfigKeyFromStorage(loadConfigsFromStorage()));

  // 新增：测试状态
  const [isTesting, setIsTesting] = useState(false);

  // Drawer 控制
  const [drawerVisible, setDrawerVisible] = useState(false);

  // 管理员密码弹窗控制
  const [adminPwdModalVisible, setAdminPwdModalVisible] = useState(false);
  const [adminPwdInput, setAdminPwdInput] = useState('');
  const [adminPwdLoading, setAdminPwdLoading] = useState(false);
  const [topVQReference, setTopVQReference] = useState(0);
  const [bottomVQReference, setBottomVQReference] = useState(0);

  // 用于保存intervalId的ref
  const intervalIdRef = useRef(null);

  // 新增：重置校准按钮功能
  const handleResetCalibration = () => {
    const keycaps = data.keycaps["00"] || {};
    Object.entries(keycaps).forEach(([key, keycap]) => {
      if (keycap) {
        // 清空最大/最小电压值
        updateKeycap(
          key.split('-')[0],
          key.split('-')[1],
          { maxKeyVoltage: undefined, minKeyVoltage: undefined },
          "00"
        );
      }
    });
    // message.success('已重置所有按键的最大/最小电压值');
  };

  // Keep configs in localStorage in sync
  useEffect(() => {
    saveConfigsToStorage(configs);
    // If selectedConfigKey is not in configs, reset to first config
    if (!configs.some(cfg => cfg.key === selectedConfigKey)) {
      if (configs.length > 0) {
        setSelectedConfigKey(configs[0].key);
        saveSelectedConfigKeyToStorage(configs[0].key);
      } else {
        setSelectedConfigKey(null);
        saveSelectedConfigKeyToStorage(null);
      }
    }
  }, [configs]);

  // Keep selectedConfigKey in localStorage in sync
  useEffect(() => {
    saveSelectedConfigKeyToStorage(selectedConfigKey);
  }, [selectedConfigKey]);

  // 计算每颗按键的最大电压值和最小电压值
  useEffect(() => {
    const keycaps = data.keycaps["00"] || {};
    Object.entries(keycaps).forEach(([key, keycap]) => {
      // 只处理有实时电压值的按键
      if (keycap && keycap.realtimeVoltage) {
        // 解析电压值为数值
        const voltage = parseInt(keycap.realtimeVoltage, 16);
        // 计算最大电压值
        if (
          (keycap.maxKeyVoltage === undefined) || (keycap.maxKeyVoltage === '0') || (keycap.maxKeyVoltage === 0) || (voltage > Number(keycap.maxKeyVoltage))
        ) {
          updateKeycap(key.split('-')[0], key.split('-')[1], { maxKeyVoltage: voltage }, "00");
        }
        // 计算最小电压值
        if (
          ((keycap.minKeyVoltage === undefined ) || (keycap.minKeyVoltage === '0') || (keycap.minKeyVoltage === 0) || (voltage < Number(keycap.minKeyVoltage))) && voltage < 4000
        ) {
          // 设置最小电压值
          updateKeycap(key.split('-')[0], key.split('-')[1], { minKeyVoltage: voltage }, "00");
        }
      }
    });
    let totalTopVQArr = [];
    let totalBottomVQArr = [];
    Object.entries(keycaps).forEach(([key, keycap]) => {
      if (keycap) {
        const top_voltage = Number(keycap.maxKeyVoltage);
        const bottom_voltage = Number(keycap.minKeyVoltage);
        if (top_voltage > 0) {
          totalTopVQArr.push(top_voltage);
        }
        if (bottom_voltage > 0) {
          totalBottomVQArr.push(bottom_voltage);
        }
      }
    });
    setTopVQReference(Math.round(totalTopVQArr.reduce((acc, cur) => acc + cur, 0) / totalTopVQArr.length));
    setBottomVQReference(Math.round(totalBottomVQArr.reduce((acc, cur) => acc + cur, 0) / totalBottomVQArr.length));
  }, [data.keycaps]);

  {/* 初始化时也执行一次 onChange 逻辑 */}
  useEffect(() => {
    // 只在 configs 或 selectedConfigKey 变化时执行
    const selected = configs.find(cfg => cfg.key === selectedConfigKey);
    if (selected) {
      setAverageVoltages(selected.averageVoltages);
      setBottomAverageVoltages(selected.bottomAverageVoltages);
      setTopPositiveOffset(selected.topPositiveOffset);
      setTopNegativeOffset(selected.topNegativeOffset);
      setBottomPositiveOffset(selected.bottomPositiveOffset);
      setBottomNegativeOffset(selected.bottomNegativeOffset);
      setKeyTriggerStroke(selected.keyTriggerStroke);
    }
    // 如果 selectedConfigKey 为 null，也可以根据需要重置参数
  }, [configs, selectedConfigKey])

  // 只在 isTesting 为 true 时才轮询 GetKeyVoltage
  useEffect(() => {
    if (isTesting) {
      GetKeyVoltage(dataQueue);
      intervalIdRef.current = setInterval(() => {
        GetKeyVoltage(dataQueue);
      }, 100);
    } else {
      if (intervalIdRef.current) {
        clearInterval(intervalIdRef.current);
        intervalIdRef.current = null;
      }
    }
    // 清理
    return () => {
      if (intervalIdRef.current) {
        clearInterval(intervalIdRef.current);
        intervalIdRef.current = null;
      }
    };
  }, [isTesting, dataQueue]);

  const changeLight = (color) => {
    if (color === 'white') {
      addToQueue(`07 03 02 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
      addToQueue(`07 03 04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
    } else if (color === 'red') {
      addToQueue(`07 03 02 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
      addToQueue(`07 03 04 00 FF 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
    } else if (color === 'green') {
      addToQueue(`07 03 02 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
      addToQueue(`07 03 04 55 FF 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
    } else if (color === 'blue') {
      addToQueue(`07 03 02 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
      addToQueue(`07 03 04 AA FF 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
    }
  };

  // 管理员配置表格列
  const columns = [
    {
      title: '配置名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <span>{text}</span>
    },
    {
      title: 'VQ平均值',
      dataIndex: 'averageVoltages',
      key: 'averageVoltages',
      render: (text) => <span>{text}</span>
    },
    {
      title: '底部VQ平均值',
      dataIndex: 'bottomAverageVoltages',
      key: 'bottomAverageVoltages',
      render: (text) => <span>{text}</span>
    },
    {
      title: '顶部正偏差',
      dataIndex: 'topPositiveOffset',
      key: 'topPositiveOffset',
      render: (text) => <span>{text}</span>
    },
    {
      title: '顶部负偏差',
      dataIndex: 'topNegativeOffset',
      key: 'topNegativeOffset',
      render: (text) => <span>{text}</span>
    },
    {
      title: '底部正偏差',
      dataIndex: 'bottomPositiveOffset',
      key: 'bottomPositiveOffset',
      render: (text) => <span>{text}</span>
    },
    {
      title: '底部负偏差',
      dataIndex: 'bottomNegativeOffset',
      key: 'bottomNegativeOffset',
      render: (text) => <span>{text}</span>
    },
    // {
    //   title: '触发行程',
    //   dataIndex: 'triggerTravel',
    //   key: 'triggerTravel',
    //   render: (text) => <span>{text}</span>
    // },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" onClick={() => handleEditConfig(record)}>编辑</Button>
          <Popconfirm
            title="确定要删除该配置吗？"
            onConfirm={() => handleDeleteConfig(record.key)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger>删除</Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 新增配置
  const handleAddConfig = () => {
    setEditConfig(null);
    form.resetFields();
    // 设置默认名字
    form.setFieldsValue({ name: `配置${configKey}` });
    setEditModalVisible(true);
  };

  // 编辑配置
  const handleEditConfig = (record) => {
    setEditConfig(record);
    form.setFieldsValue(record);
    setEditModalVisible(true);
  };

  // 删除配置
  const handleDeleteConfig = (key) => {
    const newConfigs = configs.filter(cfg => cfg.key !== key);
    setConfigs(newConfigs);
    message.success('删除成功');
    // 如果删除的是当前选中的配置，则切换到第一个配置
    if (selectedConfigKey === key) {
      if (newConfigs.length > 0) {
        setSelectedConfigKey(newConfigs[0]?.key || null);
        saveSelectedConfigKeyToStorage(newConfigs[0]?.key || null);
      } else {
        setSelectedConfigKey(null);
        saveSelectedConfigKeyToStorage(null);
      }
    }
  };

  // 提交配置（新增或编辑）
  const handleConfigOk = () => {
    form.validateFields().then(values => {
      if (editConfig) {
        // 编辑
        const updatedConfigs = configs.map(cfg => cfg.key === editConfig.key ? { ...editConfig, ...values } : cfg);
        setConfigs(updatedConfigs);
        message.success('编辑成功');
      } else {
        // 新增
        const newConfig = { ...values, key: configKey };
        const updatedConfigs = [...configs, newConfig];
        setConfigs(updatedConfigs);
        setConfigKey(configKey + 1);
        setSelectedConfigKey(configKey); // 新增后自动选中新配置
        saveSelectedConfigKeyToStorage(configKey);
        message.success('新增成功');
      }
      setEditModalVisible(false);
    });
  };

  // 取消配置弹窗
  const handleConfigCancel = () => {
    setEditModalVisible(false);
  };

  // 获取当前选中的配置
  const selectedConfig = configs.find(cfg => cfg.key === selectedConfigKey);

  // 用于ref获取“开始测试”按钮
  const startTestBtnRef = useRef(null);

  // 开始测试
  const handleStartTest = (e) => {
    if (!selectedConfigKey) {
      message.warning('请先选择一个配置');
      return;
    }
    handleResetCalibration();
    setIsTesting(true);
    setFlag('test');
    // 失去焦点，防止空格误触
    if (startTestBtnRef.current) {
      startTestBtnRef.current.blur();
    }
  };

  // 停止测试
  const handleStopTest = () => {
    setIsTesting(false);
    setFlag('logs');
  };

  // 侧边栏 Drawer 相关
  // 修改：点击管理配置按钮时先弹出密码输入框
  const handleOpenDrawer = () => {
    setAdminPwdInput('');
    setAdminPwdModalVisible(true);
  };
  const handleCloseDrawer = () => setDrawerVisible(false);

  // 管理员密码弹窗确认
  const handleAdminPwdOk = () => {
    setAdminPwdLoading(true);
    setTimeout(() => {
      if (adminPwdInput === ADMIN_PASSWORD) {
        setAdminPwdModalVisible(false);
        setDrawerVisible(true);
      } else {
        message.error('管理员密码错误');
      }
      setAdminPwdLoading(false);
    }, 400);
  };

  // 管理员密码弹窗取消
  const handleAdminPwdCancel = () => {
    setAdminPwdModalVisible(false);
    setAdminPwdInput('');
  };

  // 新增：Tab键可聚焦到自定义卡片（灯光/恢复出厂设置）
  // 让这些div可聚焦，并在回车/空格时触发点击
  const customCardKeys = [
    { color: 'white', label: '白光' },
    { color: 'red', label: '红光' },
    { color: 'green', label: '绿光' },
    { color: 'blue', label: '蓝光' },
    { color: 'factory', label: '恢复出厂设置' }
  ];

  const handleCustomCardKeyDown = (e, onClick) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <>
      <div className="keytest-content">
        <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center' }}>
          <div>固件版本号：{firmwareVersion}</div>
        </div>
        {/* 管理员配置区块 */}
        <div style={{ margin: '2em 0', background: 'rgb(27 27 27)', padding: '1em', borderRadius: '8px' }}>
          <div style={{ marginBottom: '1em', fontWeight: 'bold' }}>管理员配置（顶部/底部偏差） <Button
              style={{ marginLeft: 8 }}
              onClick={handleOpenDrawer}
              type="primary"
            >
              管理配置
            </Button></div>
            <div style={{ marginBottom: '1em', fontWeight: 'bold' }}>顶部VQ参考：{topVQReference ?? '-'}</div>
            <div style={{ marginBottom: '1em', fontWeight: 'bold' }}>底部VQ参考：{bottomVQReference ?? '-'}</div>
          {/* 配置选择下拉框和管理按钮 */}
          <div style={{ marginBottom: '1em', display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: 8 }}>选择配置：</span>
            <Select
              style={{ width: 300 }}
              value={selectedConfigKey}
              allowClear
              onClear={() => {
                setSelectedConfigKey(null);
                saveSelectedConfigKeyToStorage(null);
              }}
              onChange={key => {
                setSelectedConfigKey(key);
                saveSelectedConfigKeyToStorage(key);
                // 切换配置时设置各项参数
                const selected = configs.find(cfg => cfg.key === key);
                if (selected) {
                  setAverageVoltages(selected.averageVoltages);
                  setBottomAverageVoltages(selected.bottomAverageVoltages);
                  setTopPositiveOffset(selected.topPositiveOffset);
                  setTopNegativeOffset(selected.topNegativeOffset);
                  setBottomPositiveOffset(selected.bottomPositiveOffset);
                  setBottomNegativeOffset(selected.bottomNegativeOffset);
                  setKeyTriggerStroke(selected.keyTriggerStroke);
                }
              }}
              placeholder="请选择配置"
              options={[
                { value: null, label: '无' },
                ...configs.map(cfg => ({
                  value: cfg.key,
                  label: `${cfg.name || `配置${cfg.key}`}`
                }))
              ]}
              disabled={configs.length === 0 || isTesting}
            />
            {!isTesting ? (
              <Button
                style={{ marginLeft: 8 }}
                onClick={handleStartTest}
                disabled={selectedConfigKey === null}
                type="primary"
                tabIndex={-1}
                ref={startTestBtnRef}
              >
                开始测试
              </Button>
            ) : (
              <Button
                style={{ marginLeft: 8 }}
                onClick={handleStopTest}
                danger
                tabIndex={-1}
                type="primary"
              >
                停止测试
              </Button>
            )}
            {/* 新增：重置校准按钮 */}
            {/* <Button
              style={{ marginLeft: '1em' }}
              onClick={handleResetCalibration}
              type="default"
              tabIndex={-1}
            >
              重置校准
            </Button> */}
          </div>
          {/* 当前选中配置的信息展示 */}
          <div style={{ marginBottom: '1em' }}>
            <Descriptions
              title="当前选中配置"
              bordered
              size="small"
              column={4}
              style={{ background: '#232323', borderRadius: 4, padding: 8 }}
            >
              <Descriptions.Item label="配置名称">
                {selectedConfig?.name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="顶部VQ平均值">
                {selectedConfig?.averageVoltages ?? '-'}
              </Descriptions.Item>
              <Descriptions.Item label="底部VQ平均值">
                {selectedConfig?.bottomAverageVoltages ?? '-'}
              </Descriptions.Item>
              <Descriptions.Item label="预留位">
                {'-'}
              </Descriptions.Item>
              <Descriptions.Item label="顶部正偏差">
                {selectedConfig?.topPositiveOffset ?? '-'}
              </Descriptions.Item>
              <Descriptions.Item label="顶部负偏差">
                {selectedConfig?.topNegativeOffset ?? '-'}
              </Descriptions.Item>
              <Descriptions.Item label="底部正偏差">
                {selectedConfig?.bottomPositiveOffset ?? '-'}
              </Descriptions.Item>
              <Descriptions.Item label="底部负偏差">
                {selectedConfig?.bottomNegativeOffset ?? '-'}
              </Descriptions.Item>
            </Descriptions>
          </div>
        </div>
        {/* 管理员密码输入弹窗 */}
        <Modal
          title="管理员认证"
          open={adminPwdModalVisible}
          onOk={handleAdminPwdOk}
          onCancel={handleAdminPwdCancel}
          confirmLoading={adminPwdLoading}
          destroyOnClose
          maskClosable={false}
        >
          <Form
            onFinish={handleAdminPwdOk}
            layout="vertical"
          >
            <Form.Item
              // label="请输入管理员密码"
              required
            >
              <Input.Password
                value={adminPwdInput}
                onChange={e => setAdminPwdInput(e.target.value)}
                onPressEnter={handleAdminPwdOk}
                placeholder="管理员密码"
                autoFocus
              />
            </Form.Item>
          </Form>
        </Modal>
        {/* 配置管理 Drawer */}
        <Drawer
          title="配置管理"
          placement="right"
          width={1200}
          onClose={handleCloseDrawer}
          open={drawerVisible}
          destroyOnClose
        >
          <Button type="primary" onClick={handleAddConfig} style={{ marginBottom: '1em' }}>新增配置</Button>
          <Table
            columns={columns}
            dataSource={configs}
            rowKey="key"
            pagination={false}
            size="small"
          />
        </Drawer>
        {/* 配置编辑/新增弹窗 */}
        <Modal
          title={editConfig ? "编辑配置" : "新增配置"}
          open={editModalVisible}
          onOk={handleConfigOk}
          onCancel={handleConfigCancel}
          destroyOnClose
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={defaultConfig}
          >
            <Form.Item
              label="配置名称"
              name="name"
              rules={[
                { required: true, message: '请输入配置名称' },
                { max: 32, message: '名称不能超过32个字符' }
              ]}
            >
              <Input maxLength={32} placeholder="请输入配置名称" />
            </Form.Item>
            <Form.Item
              label="顶部VQ平均值"
              name="averageVoltages"
              rules={[{ required: true, message: '请输入顶部VQ平均值' }]}
            >
              <InputNumber min={-100000} max={100000} style={{ width: '100%' }} />
            </Form.Item>
            <Form.Item
              label="底部VQ平均值"
              name="bottomAverageVoltages"
              rules={[{ required: true, message: '请输入底部VQ平均值' }]}
            >
              <InputNumber min={-100000} max={100000} style={{ width: '100%' }} />
            </Form.Item>
            <Form.Item
              label="顶部正偏差"
              name="topPositiveOffset"
              rules={[{ required: true, message: '请选择顶部正偏差' }]}
            >
              <Select style={{ width: '100%' }} placeholder="请选择偏差值">
                <Select.Option value={100}>A (100)</Select.Option>
                <Select.Option value={200}>B (200)</Select.Option>
                <Select.Option value={300}>C (300)</Select.Option>
                <Select.Option value={400}>D (400)</Select.Option>
                <Select.Option value={500}>E (500)</Select.Option>
                <Select.Option value={600}>F (600)</Select.Option>
                <Select.Option value={700}>G (700)</Select.Option>
                <Select.Option value={800}>H (800)</Select.Option>
                <Select.Option value={900}>I (900)</Select.Option>
                <Select.Option value={1000}>J (1000)</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              label="顶部负偏差"
              name="topNegativeOffset"
              rules={[{ required: true, message: '请选择顶部负偏差' }]}
            >
              <Select style={{ width: '100%' }} placeholder="请选择偏差值">
              <Select.Option value={100}>A</Select.Option>
                <Select.Option value={200}>B</Select.Option>
                <Select.Option value={300}>C</Select.Option>
                <Select.Option value={400}>D</Select.Option>
                <Select.Option value={500}>E</Select.Option>
                <Select.Option value={600}>F</Select.Option>
                <Select.Option value={700}>G</Select.Option>
                <Select.Option value={800}>H</Select.Option>
                <Select.Option value={900}>I</Select.Option>
                <Select.Option value={1000}>J</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              label="底部正偏差"
              name="bottomPositiveOffset"
              rules={[{ required: true, message: '请选择底部正偏差' }]}
            >
              <Select style={{ width: '100%' }} placeholder="请选择偏差值">
                <Select.Option value={100}>A</Select.Option>
                <Select.Option value={200}>B</Select.Option>
                <Select.Option value={300}>C</Select.Option>
                <Select.Option value={400}>D</Select.Option>
                <Select.Option value={500}>E</Select.Option>
                <Select.Option value={600}>F</Select.Option>
                <Select.Option value={700}>G</Select.Option>
                <Select.Option value={800}>H</Select.Option>
                <Select.Option value={900}>I</Select.Option>
                <Select.Option value={1000}>J</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              label="底部负偏差"
              name="bottomNegativeOffset"
              rules={[{ required: true, message: '请选择底部负偏差' }]}
            >
              <Select style={{ width: '100%' }} placeholder="请选择偏差值">
                <Select.Option value={100}>A</Select.Option>
                <Select.Option value={200}>B</Select.Option>
                <Select.Option value={300}>C</Select.Option>
                <Select.Option value={400}>D</Select.Option>
                <Select.Option value={500}>E</Select.Option>
                <Select.Option value={600}>F</Select.Option>
                <Select.Option value={700}>G</Select.Option>
                <Select.Option value={800}>H</Select.Option>
                <Select.Option value={900}>I</Select.Option>
                <Select.Option value={1000}>J</Select.Option>
              </Select>
            </Form.Item>
            {/* <Form.Item
              label="触发行程"
              name="triggerTravel"
              rules={[{ required: true, message: '请输入触发行程' }]}
            >
              <InputNumber min={0} max={10000} style={{ width: '100%' }} />
            </Form.Item> */}
          </Form>
        </Modal>
        {/* 传递当前选中的配置到 KeyboardTest */}
        <KeyboardTest
          itemPage={flag}
          tmpMeedAdjustKeys={tmpMeedAdjustKeys}
          selectedConfig={selectedConfig}
        />
        <div style={{ marginTop: '-84px' }}>
          <KeyTestLogs key="keytestlogs" />
        </div>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '1em', justifyContent: 'center', marginTop: '2em', marginBottom: '3em' }}>
          {/* 可聚焦的自定义卡片 */}
          <div
            className='custom-card'
            tabIndex={0}
            onClick={() => changeLight('white')}
            onKeyDown={e => handleCustomCardKeyDown(e, () => changeLight('white'))}
            aria-label="白光"
            role="button"
          >白光</div>
          <div
            className='custom-card'
            tabIndex={0}
            onClick={() => changeLight('red')}
            onKeyDown={e => handleCustomCardKeyDown(e, () => changeLight('red'))}
            aria-label="红光"
            role="button"
          >红光</div>
          <div
            className='custom-card'
            tabIndex={0}
            onClick={() => changeLight('green')}
            onKeyDown={e => handleCustomCardKeyDown(e, () => changeLight('green'))}
            aria-label="绿光"
            role="button"
          >绿光</div>
          <div
            className='custom-card'
            tabIndex={0}
            onClick={() => changeLight('blue')}
            onKeyDown={e => handleCustomCardKeyDown(e, () => changeLight('blue'))}
            aria-label="蓝光"
            role="button"
          >蓝光</div>
          <div
            className='custom-card'
            style={{ width: '300px' }}
            tabIndex={0}
            onClick={() => addToQueue('07 00 01 01')}
            onKeyDown={e => handleCustomCardKeyDown(e, () => addToQueue('07 00 01 01'))}
            aria-label="恢复出厂设置"
            role="button"
          >恢复出厂设置</div>
        </div>
      </div>
    </>
  );
};

export default KeyTest;