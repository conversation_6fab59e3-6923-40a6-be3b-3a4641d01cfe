import './Keytest.css'

import keyTestDate from './keyTestDate.jsx'
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Waveform from './Waveform.jsx';
import { Button } from 'antd';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';

function KeyTestLogs() {
  const { addToQueue } = useHandleDevice();
  const { t } = useTranslation();
  const mainItem = keyTestDate.filter(i => i.station == 'main');
  const middleItem = keyTestDate.filter(i => i.station == 'middle');
  const numberItem = keyTestDate.filter(i => i.station == 'number');
  const lnumberItem = keyTestDate.filter(i => i.station == 'lnumber');
  const [activedKeys, setActivedKeys] = useState([]); //这个是监控按键是否有按下的变色
  const [pressedKeys, setPressedKeys] = useState([]); //这个是监控按键是否按下的变色
  const [KeyPushLogs, setKeyPushLogs] = useState([]);
  const [apmreset, setApmreset] = useState() //控制重置让apm值为0

  const resetKeyPushs = () => {
    setActivedKeys([]);
    setPressedKeys([]);
    setKeyPushLogs([]);
    setApmreset('stop');
  };

  const checkActived = (keyCode) => {
    if (keyCode === "") {
      return false
    }
    if (keyCode === 'PrintScreen'){
      return activedKeys.includes(keyCode) || activedKeys.includes('F13')
    }
    return activedKeys.includes(keyCode)
  }

  const checkPressed = (keyCode) => {
    if (keyCode === 'PrintScreen'){
      return pressedKeys.includes(keyCode) || pressedKeys.includes('F13')
    }
    return pressedKeys.includes(keyCode)
  }

  // 转化时间
  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    let millisens = String(date.getMilliseconds()).padStart(2, '0');
    millisens = millisens.split('')
    return `${hours}:${minutes}:${seconds}.${millisens[0]}`;

  }

  const find_by_key_value = (key_value) => {
    let key_date = keyTestDate.find(item => item.key_value === key_value.code || item.key_value_mac &&  item.key_value_mac ===  key_value.code )
    if (key_date && typeof key_date === 'object') {
      return key_date.id
    } else {
      return ""
    }
  }

  useEffect(() => {
    const handleKeyDown = (event) => {
      // event.preventDefault(); //所有的其他功能按键进行屏蔽
      const timeOrigin = performance.timeOrigin;
      setApmreset('start');

      // 存储按键这块的是否变色
      if (!activedKeys.includes(event.code)) {
        setActivedKeys(activedKeys => [event.code, ...activedKeys]);
        setPressedKeys(pressedKeys => [event.code, ...pressedKeys]);
      }

      // 储存按键这块进行打日志
      if (!event.repeat) {
        setKeyPushLogs(KeyPushLogs => [
          {
            code: event.code,
            timeStamp: formatTime(timeOrigin + event.timeStamp),
            type: event.type
          },
          ...KeyPushLogs
        ])
      }
    };
    const handleKeyUp = (event) => {
      console.log("handleKeyUp",event)
      const timeOrigin = performance.timeOrigin;

      // 存储按键这块的是否变色
      if (!activedKeys.includes(event.code)) {
         setPressedKeys(pressedKeys => [event.code, ...pressedKeys]);
      }

      // 按键松开时移除激活色
      setActivedKeys(activedKeys => activedKeys.filter(code => code !== event.code));

      if (!event.repeat) {
        setKeyPushLogs(KeyPushLogs => [
          {
            code: event.code,
            timeStamp: formatTime(timeOrigin + event.timeStamp),
            type: event.type
          },
          ...KeyPushLogs
        ])
      }
    };
    // 超过十个挤掉一个
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [activedKeys, pressedKeys]);

  return <>
    <div className='keytest-keyboard'>
      <div className='keybroad-groups'>
        <div className='keybroad-main'>
          {mainItem.map(keyitem => (
            <button className={`keybroad-button ${checkActived(keyitem.key_value) && 'active'}  ${checkPressed(keyitem.key_value)  && 'pressed'}  ${keyitem.meth === '' ? 'nondeclass' : ''}`} key={keyitem.id} style={{ width: keyitem.width }} >
              <div className='keybroad-button-icon' >
                  <svg aria-hidden="true"  width='36'  height='36'>
                     <use href= {`#icon-Icon-Wrapper-${keyitem.id}` }/>
                 </svg>
              </div>
            </button>
          ))}

        </div>
        <div className='keybroad-middle'>
          {middleItem.map(keyitem => (
            <button className={`keybroad-button ${checkActived(keyitem.key_value) && 'active'}  ${checkPressed(keyitem.key_value) && 'pressed'}  ${keyitem.meth === '' ? 'nondeclass' : ''}`} key={keyitem.id} style={{ width: keyitem.width }} >
              <div className='keybroad-button-icon' style={{ backgroundImage: `url(/keytest/Icon-Wrapper-${keyitem.id}.svg)` }} >
              </div>
            </button>
          ))}
        </div>
        <div className='keybroad-number'>
          <div className='three-number-keycap'>
            {numberItem.map(keyitem => (
              <button className={`keybroad-button ${checkActived(keyitem.key_value) && 'active'}  ${checkPressed(keyitem.key_value) && 'pressed'}  ${keyitem.meth === '' ? 'nondeclass' : ''}`} key={keyitem.id} style={{ width: keyitem.width }} >
                <div className='keybroad-button-icon' style={{ backgroundImage: `url(/keytest/Icon-Wrapper-${keyitem.id}.svg)` }} >
                </div>
              </button>
            ))}
          </div>
          <div className="last-col-keycap">
            {lnumberItem.map(keyitem => (
              <button className={`keybroad-button ${checkActived(keyitem.key_value) && 'active'}  ${checkPressed(keyitem.key_value) && 'pressed'}  ${keyitem.meth === '' ? 'nondeclass' : ''}`} key={keyitem.id} style={{ height: keyitem.heght }} >
                <div className='keybroad-button-icon' style={{ backgroundImage: `url(/keytest/Icon-Wrapper-${keyitem.id}.svg)` }} >
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
    <div className='operation-panel'>
      <div className='operation-panel-menus'>
        <div className='operation-panel-menu'>
          <div className='operation-panel-groups'>
            <div className='operation-panel-group'>
              <button className='operation-panel-group-button unpress'>
              </button>
              <span>Unpress</span>
            </div>
            <div className='operation-panel-group'>
              <button className='operation-panel-group-button active'>
              </button>
              <span>Active</span>
            </div>
            <div className='operation-panel-group'>
              <button className='operation-panel-group-button pressed'>
              </button>
              <span>Pressed</span>
            </div>
          </div>
          <Button type="primary" style={{ width: '82px', fontSize: '16px' }} onClick={resetKeyPushs}>
            {t('keytest.reset_pushs')}
          </Button>

        </div>
        <div className='operation-panel-alert'>
          <div className='operation-panel-content'>
            <div className='operation-panel-icon'>
              <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                <path d="M7.5 0.5C3.63438 0.5 0.5 3.63438 0.5 7.5C0.5 11.3656 3.63438 14.5 7.5 14.5C11.3656 14.5 14.5 11.3656 14.5 7.5C14.5 3.63438 11.3656 0.5 7.5 0.5ZM8 10.875C8 10.9438 7.94375 11 7.875 11H7.125C7.05625 11 7 10.9438 7 10.875V6.625C7 6.55625 7.05625 6.5 7.125 6.5H7.875C7.94375 6.5 8 6.55625 8 6.625V10.875ZM7.5 5.5C7.30374 5.49599 7.11687 5.41522 6.97948 5.275C6.8421 5.13478 6.76515 4.9463 6.76515 4.75C6.76515 4.5537 6.8421 4.36522 6.97948 4.225C7.11687 4.08478 7.30374 4.00401 7.5 4C7.69626 4.00401 7.88313 4.08478 8.02052 4.225C8.1579 4.36522 8.23485 4.5537 8.23485 4.75C8.23485 4.9463 8.1579 5.13478 8.02052 5.275C7.88313 5.41522 7.69626 5.49599 7.5 5.5Z" fill="#D7D7DB" />
              </svg>
            </div>
            <span>{t('keytest.browser_prompt_content')}</span>
          </div>

        </div>
      </div>
    </div>

    <div>

    </div>
  </>
}
export default KeyTestLogs;