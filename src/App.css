body {
  background-color: #000000;
  color: white;
  margin: 0;
  padding: 0;
}

:root {
  --bs-blue: #1668dc;
  --keycap-width: 4em;
  --keycap-inside-width: 0.2em;
  --keycap-inside-height: 0em;
}

.d-flex {
  display: flex;
}

.align-items-center {
  align-items: center;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.keycap {
  outline: none;
  border: none;
  /* padding: 2px; */
  display: flex;
  justify-content: center;
  align-items: center;
  background: #1C1D22;
  color: white;
  cursor: pointer;
  border-radius: 2px;
  font-weight: bold;
  border: 2px solid #1C1D22;
  margin: 2px 1px;
  user-select: none;
  transition: all 300ms ease-in-out;
  /* will-change: transform; */
  /* box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px; */
}

.keycap:hover {
  border: 2px solid #353535;
}

.keycap:hover .keycap-inside {
  transition: all 300ms ease-in-out !important;
  background: linear-gradient(180deg, rgba(0,0,0,1) 50%, rgba(29,29,29,1) 100%) !important;
}

.keycap.advanced-key {
  border-color: #1668dc;
  pointer-events: none;
  cursor: not-allowed;
}

.keycap.active {
  border: 2px solid #ff5656;
}

.keycap-inside {
  word-wrap: break-word; /* 长单词自动换行 */
  word-break: break-word;
  overflow: hidden;
  font-size: 0.9em;
  background: #1C1D22;
  transition: all 300ms ease-in-out !important;
}

.keycap-text {
  color: #EFF0F5;
  opacity: 0.35;
  font-weight: 500;
}

.keycap-u1 .keycap-inside {
  border-radius: 2px;
  width: 100%;
  height: calc(1 * var(--keycap-width) - var(--keycap-inside-height));
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.keycap-u1_25 .keycap-inside {
  border-radius: 2px;
  width: 100%;
  height: calc(1 * var(--keycap-width) - var(--keycap-inside-height));
  display: flex;
  justify-content: center;
  align-items: center;
}

.keycap-u1_5 .keycap-inside {
  border-radius: 2px;
  width: 100%;
  height: calc(1 * var(--keycap-width) - var(--keycap-inside-height));
  display: flex;
  justify-content: center;
  align-items: center;
}

.keycap-u1_75 .keycap-inside {
  border-radius: 2px;
  width: 100%;
  height: calc(1 * var(--keycap-width) - var(--keycap-inside-height));
  display: flex;
  justify-content: center;
  align-items: center;
}

.keycap-u2 .keycap-inside {
  border-radius: 2px;
  width: 100%;
  height: calc(1 * var(--keycap-width) - var(--keycap-inside-height));
  display: flex;
  justify-content: center;
  align-items: center;
}

.keycap-u2_25 .keycap-inside {
  border-radius: 2px;
  width: 100%;
  height: calc(1 * var(--keycap-width) - var(--keycap-inside-height));
  display: flex;
  justify-content: center;
  align-items: center;
}

.keycap-u2_75 .keycap-inside {
  border-radius: 2px;
  width: 100%;
  height: calc(1 * var(--keycap-width) - var(--keycap-inside-height));
  display: flex;
  justify-content: center;
  align-items: center;
}

.keycap-u6_25 .keycap-inside {
  border-radius: 2px;
  width: 100%;
  height: calc(1 * var(--keycap-width) - var(--keycap-inside-height));
  display: flex;
  justify-content: center;
  align-items: center;
}

.keycap-u1 {
  width: var(--keycap-width);
}

/* 1.25u 键 */
.keycap-u1_25 {
  width: calc(1.2595 * var(--keycap-width));
}

/* 1.5u 键 */
.keycap-u1_5 {
  width: calc(1.5168 * var(--keycap-width));
}

/* 1.75u 键 */
.keycap-u1_75 {
  width: calc(1.7768 * var(--keycap-width));
}

/* 2u 键 */
.keycap-u2 {
  width: calc(2.0402 * var(--keycap-width));
}

/* 2.25u 键 */
.keycap-u2_25 {
  width: calc(2.36 * var(--keycap-width));
}

/* 2.75u 键 */
.keycap-u2_75 {
  width: calc(2.86 * var(--keycap-width));
}

/* 6.25u 键 */
.keycap-u6_25 {
  width: calc(6.6 * var(--keycap-width));
}

.keycap-u6_5 {
  width: calc(6.66 * var(--keycap-width));
}

.keycap:hover {
  /* zoom: 90%; */
}

.keycap-block {
  padding: 10px 26px;
  border-radius: 6px;
  background: #141416;
  cursor: pointer;
  margin-right: 5px;
  margin-bottom: 10px;
  transition: all 0.2s;
  border: 1px solid #2F2F2F;
}

.keycap-block-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.keycap-block:hover {
  background: #202020;
}

.function-area-title {
  font-size: 16px;
  margin-bottom: 1em;
  font-weight: bold;
  margin-top: 1em;
}

.nav-pills .menu.nav-link {
  color: white;
  padding-left: 2.2em;
  padding-top: 1em;
  padding-bottom: 1em;
  border-radius: 0;
  font-size: 1em;
}

.nav-pills .menu.nav-link.active {
  background-color: #45454C;
}

.nav-pills .underline.nav-link {
  color: white;
  background-color: transparent;
  border-radius: 0;
}

.nav-pills .underline.nav-link.active {
  border-bottom: 2px solid white;
}

.right-layout-width {
  width: calc(100vw - 210px);
}

.pill-tab.nav-link {
  border-radius: 2em;
  min-width: 5em;
}

/* 动画效果 */
.load-container {
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  top: 45%;
}
.load-container .animate-container {
  width: 50px;
  height: 60px;
  text-align: center;
  font-size: 10px;
}
.load-container .animate-container .boxLoading {
  background-color: #3974FF;
  height: 100%;
  width: 6px;
  display: inline-block;
  -webkit-animation: stretchdelay 1.2s infinite ease-in-out;
  animation: stretchdelay 1.2s infinite ease-in-out;
}
.load-container .animate-container .boxLoading2 {
  -webkit-animation-delay: -1.1s;
  animation-delay: -1.1s;
}
.load-container .animate-container .boxLoading3 {
  -webkit-animation-delay: -1s;
  animation-delay: -1s;
}
.load-container .animate-container .boxLoading4 {
  -webkit-animation-delay: -0.9s;
  animation-delay: -0.9s;
}
.load-container .animate-container .boxLoading5 {
  -webkit-animation-delay: -0.8s;
  animation-delay: -0.8s;
}
@-webkit-keyframes stretchdelay {
  0%, 40%, 100% {
    -webkit-transform: scaleY(0.4);
  }
  20% {
    -webkit-transform: scaleY(1);
  }
}
@keyframes stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
    -webkit-transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
    -webkit-transform: scaleY(1);
  }
}

/* 性能界面样式 */
.trigger-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.trigger-label {
  width: 2em;
  margin-bottom: 0.5em;
  margin-left: 1em;
}

.no-device {
  background: red;
  animation: no_device_pulse 1.5s infinite;
}

.connected-device {
  background: rgb(26, 245, 26);
  animation: connected_pulse 1.5s infinite;
}

.status-light {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.6), 0 0 20px rgba(0, 255, 0, 0.4);
}

@keyframes no_device_pulse {
  0% {
    box-shadow: 0 0 8px rgba(255, 30, 0, 0.6), 0 0 20px rgba(255, 0, 0, 0.4);
  }
  50% {
    box-shadow: 0 0 8px rgba(255, 30, 0, 0.8), 0 0 30px rgba(255, 0, 0, 0.6);
  }
  100% {
    box-shadow: 0 0 8px rgba(255, 30, 0, 0.6), 0 0 20px rgba(255, 0, 0, 0.4);
  }
}

@keyframes connected_pulse {
  0% {
    box-shadow: 0 0 8px rgba(0, 255, 0, 0.6), 0 0 20px rgba(0, 255, 0, 0.4);
  }
  50% {
    box-shadow: 0 0 8px rgba(0, 255, 0, 0.8), 0 0 30px rgba(0, 255, 0, 0.6);
  }
  100% {
    box-shadow: 0 0 8px rgba(0, 255, 0, 0.6), 0 0 20px rgba(0, 255, 0, 0.4);
  }
}

@font-face {
  font-family: vivoSans-Regular;
  src:
       url(./assets/font/vivoSans/vivoSans-Regular.ttf);
}
@font-face {
  font-family: "vivo Sans";
  src:
       url(./assets/font/vivoSans/vivoSans-Regular.ttf);
}

:root {
  --color-background-primary: #696969;
  --color-text-primary: #c8c8c8;
  --font-family-sans-serif: vivoSans-Regular;
  ---divider: #FFFFFF1A;
  --background-background-1: #000000;
  --background-background-2: #0f0f10;
  --background-background-3: #1d1e20;
  --keyboard-keyboard-1: #121316;
  --keyboard-keyboard-2: #23242A;
  --keyboard-keyboard-3: #626469;
  --keyboard-keyboard-4: #000000;
  --keyboard-keyboard-5: #121212;
  --keyboard-keyboard-6: #252528;
  --keyboard-keyboard-7: #252528;
  --keyboard-keyboard-8: #0E0E0E;
  --keyboard-keyboard-9: #131315;
  --keyboard-keyboard-10: #000000;
  --keyboard-keyboard-11: #58595B;
  --keyboard-keyboard-12: #121212;
  --keyboard-keyboard-13: #121212;
  --keyboard-keyboard-14: #262627;
  /* --stroke-stroke-1: rgba(#FFFFFF, 0.1);
  --stroke-stroke-1: rgba(#FFFFFF, 0.15);
  --stroke-stroke-2: rgba(#FFFFFF, 0.3);
  --stroke-divider: rgba(#FFFFFF, 0.1); */
  --stroke-stroke-1: #FFFFFF;
  --stroke-stroke-1: #FFFFFF;
  --stroke-stroke-2: rgba(255, 255, 255, 0.3);
  --stroke-divider: #FFFFFF;
  --stroke-stroke-42: rgba(255, 255, 255, 0);
  /* --stroke-stroke-42: #ffffff; */
  --fil-fill-0: rgba(#EFF0F5, 0);
  --fil-fill-1: rgba(#EFF0F5, 0.18);
  --fil-fill-2: rgba(#EFF0F5, 0.12);
  --fil-fill-21: rgba(#E8EAFC, 0.1);
  --fil-fill-3: rgba(#E8EAFC, 0.08);
  --fil-fill-4: rgba(#E8EAFC, 0.04);
  --fil-fill-5: rgba(#000000, 0.5);
  --fill-new-fill-new-fill-1: #0f0f10;
  --fill-new-fill-new-fill-2: #1d1e20;
  --fill-new-fill-new-fill-3: #1d1e20;
  --fill-new-fill-new-fill-4: rgba(235, 237, 250, 0.15);
  --fill-new-fill-new-fill-4-1: #ffffff;
  --fill-new-fill-new-fill-42: #ffffff;
  --fill-new-fill-new-fill-43: rgba(235, 237, 250, 0.15);
  --fill-new-fill-new-fill-5: rgba(29, 30, 32, 0.50);
  --text-text-1: #D7D7DB;
  --text-text-1-1: rgba(215, 215, 219, 0.80);
  --text-text-2: rgba(215, 215, 219, 0.60);
  --text-text-3: rgba(215, 215, 219, 0.40);
  --text-text-32: rgba(215, 215, 219, 0.35);
  --text-text-4: rgba(215, 215, 219, 0.25);
  --brand-1: #196BE5;
  --brand-2: #e8f5ff;
  --brand-3: #bfe1ff;
  --brand-4: #96cbff;
  --brand-5: #6eb1ff;
  --brand-6: #418ef2;
  --brand-7: #0b4dbf;
  --brand-8: #023499;
  --brand-9: #002273;
  --brand-10: #00144d;
  --Brand-Brand: #196BE5;
  --special-special-stroke-1: #262626;
  --special-special-button-2: #2c2c30;
  --special-special-button-1: #0d0d0d;
  --special-special-text-1: #808080;
  --slider-1: #f5f5f5;
  --slider-2: #b2b2b2;
  --slider-3: #404040;
  --slider-4: #262626;
  --light-1: #262626;
  --light-2: #7a7c85;
  --netural-netural-100: #ffffff;
  --netural-netural-95: #f0f0f5;
  --netural-netural-90: #e5e5e5;
  --netural-netural-85: #d9d9d9;
  --netural-netural-80: #cccccc;
  --netural-netural-70: #b2b2b2;
  --netural-netural-60: #999999;
  --netural-netural-65: #6a6a6a;
  --netural-netural-50: #808080;
  --netural-netural-40: #666666;
  --netural-netural-35: #595959;
  --netural-netural-30: #4d4d4d;
  --netural-netural-20: #333333;
  --netural-netural-15: #262626;
  --netural-netural-10: #1a1a1a;
  --netural-netural-5: #0d0d0d;
  --netural-netural-0: #000000;
  --character-primary-inverse: #ffffff;
  /* --1: #000000; */
}


kbd {
  --kbd-color-background: var(--color-background-primary);
  --kbd-color-border: #7d7d7d;
  --kbd-color-text: var(--color-text-primary);
  background-color: var(--kbd-color-background);
  border-radius: 0.25rem;
  border: 1px solid var(--kbd-color-border);
  box-shadow: 0 2px 0 1px var(--kbd-color-border);
  color: var(--kbd-color-text);
  cursor: default;
  display: inline-block;
  font-family: var(--font-family-sans-serif);
  font-size: 1em;
  line-height: 1;
  min-width: 1.5em;
  min-height: 1.5em;
  padding: 2px 5px;
  position: relative;
  text-align: center;
  top: -1px;
}

/* 灵动键 */
.key-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 90px;
  height: 90px;
  background: #3b3d4d5d;
  border-radius: 8px;
  margin-top: 0.8em;
  cursor: pointer;
  border: 2px solid transparent;
  overflow: hidden;
}

.key-container.active {
  border: 2px solid var(--bs-blue);
}

/* SOCD 样式 */

.advanced-key-item-wrapper {
  width: 100%;
  margin-bottom: 0.8em;
}

.advanced-key-item {
  width: 100%;
  height: 68px;
  background: #1D1E22;
  border-radius: 8px;
  padding: 16px 24px;
  cursor: pointer;
  border: 2px solid transparent;
  color: #EFF0F573;
}

.advanced-key-item .delete-button {
  color: #EFF0F573;
  margin-left: 12px;
  opacity: 0;
}

.advanced-key-item:hover .delete-button {
  opacity: 1 !important;
}

.advanced-key-item .icon {
  color: #dddddd;
  display: flex;
}
.advanced-key-backgroud{
  background: rgba(78, 73, 108, 0.06);
}
button.disable{
  border-radius: 6px;
  border: 1px solid var(--Brand-Brand, #196BE5);
  background: linear-gradient(0deg, var(--fill-new-fill-new-fill-1, rgba(15, 15, 16, 0.50)) 0%, var(--fill-new-fill-new-fill-1, rgba(15, 15, 16, 0.50)) 100%), var(--Brand-Brand, #196BE5);
  /* drop-shadow/button-primary */
  box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.04);
  pointer-events: none;
  cursor: not-allowed;
}
.advanced-key-item.active {
  /* border: 2px solid var(--bs-blue); */
  background: #2F3037;
}

.keycap-item {
  display: flex;
  width: 36px;
  height: 36px;
  justify-content: center;
  align-items: center;
  word-wrap: break-word;
  word-break: break-all;
  border-radius: 3px;
  border: 1px solid rgba(239, 240, 245, 0.04);
  background: rgba(239, 240, 245, 0.04);
}

.divider-line {
  margin: 0px 24px;
  align-items: center;
  background: #EFF0F51F;
  width: 2px;
  height: 16px;
  border-radius: 2px;
}

.socd-mode {
  margin-right: 0.5em;
}

.selected-socd-mode {
  width: 100%;
  height: 50px;
  background: #3b3d4d5d;
  color: #ffffff;
  border-radius: 8px;
  padding: 0 1em;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border: 2px solid transparent;
}

.selected-socd-mode.active {
  border: 2px solid var(--bs-blue);
}

#add-socd-item {
  width: 84px;
  background-color: transparent !important;
  border: 2px solid var(--bs-blue);
  font-size: larger;
  cursor: pointer;
  padding: 3px 3px 7px 3px;
}

.socd-assigned {
  cursor: default;
  border-color: #52c41a;
}

.keycap.active {
  pointer-events: auto !important;
}

.socd-paired {
  border-color: #53c41abb;
}

.ant-menu-item-selected path {
  fill: #1668dc;
}

.ant-menu-item {
  padding: 29px 0 29px 24px;
  margin-block: 8px !important;
}

.ant-layout-sider {
  margin-left: 19px;
  flex: none !important;
  width: 270px !important;
  max-width: 270px !important;
}

/* 灵动键列表 */
.advanced-key-list {
  display: flex;
  gap: 1em;
  align-items: center;
  margin-bottom: 8px;
  justify-content: space-between;
}

.ant-spin-text {
  margin-top: 28px;
  margin-left: 10px;
}

.ant-spin-fullscreen .ant-spin-spinning {
  margin-top: -200px;
}

.advanced-key-list-item {
  width: 49%;
  height: 110px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: inset 0 0 0 1px #282A33;
  cursor: pointer;
  transition: all 200ms ease-in-out;
  padding: 8px 40px;
  background-color: rgba(239, 240, 245, 0.04);
}

.advanced-key-list-item.active {
  box-shadow: inset 0 0 0 2px var(--bs-blue);
}

.advanced-key-list-item:hover {
  box-shadow: inset 0 0 0 2px var(--bs-blue);
}

.advanced-key-list-item .img-container {
  width: 64px;
  text-align: center;
}

.advanced-key-list-item .text {
  color: rgba(239, 240, 245, 0.45);
  width: 150px;
  font-size: 14px;
}

/* DKS 样式 */
.dks-container {
  display: flex;
  align-items: center;
  gap: 1em;
  margin-top: 1.5em;
}

.dks-key-container {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 50px;
  height: 50px;
  background: #3b3d4d5d;
  border-radius: 8px;
  cursor: pointer;
  border: 2px solid transparent;
  overflow: hidden;
}

.dks-setting-bar {
  width: 472px;
  height: 40px;
  background: #0B0C0E;
  border: 1px solid #28292D;
  border-radius: 24px;
  display: flex;
  justify-content: space-between;
  padding: 0 0.5em;
  align-items: center;
}

.status-point-wrapper {
  background-color: transparent;
  width: 24px;
  height: 24px;
  border-radius: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: absolute;
}

.status-point-wrapper.active {
  background-color: #D9D9D9;
}

.tmp-status-point-wrapper {
  background-color: transparent;
  width: 24px;
  height: 24px;
  border-radius: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: absolute;
  border: 1px solid #d9d9d908;
}

.tmp-status-point-wrapper.active {
  background-color: #d9d9d91f;
}

.status-point {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
  background: #000000;
}

.status-point-wrapper.active .status-point {
  background: #0B0C0E;
}

.resize-handle {
  background: transparent;
  height: 18px;
  width: 10px;
  cursor: ew-resize;
  position: absolute;
  top: 2px;
  right: -3px;
  color: #c0c0c0;
}

.custom-card-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 1em;
  background: #17181C;
  border-radius: 12px;
  /* background: var(--background-background-3, #1D1E20); */
  width: 410px;
  gap: 1em;
  height: 400px;
  border-radius: 1em;
  overflow: auto;
  overscroll-behavior: contain;
  box-shadow: inset 0 0 0 1px #282A33;
}

.custom-card-container::before,.custom-card-container::after{
  background: #17181C;
  /* background: var(--background-background-3, #1D1E20); */
}

.custom-card-container::-webkit-scrollbar {
  width: 5px;
}

.custom-card-container::-webkit-scrollbar-track {
background-color: black;
}

.custom-card-container::-webkit-scrollbar-thumb {
  background: #4e4e4e;
  border-radius: 25px;
}

.custom-card-inside-container {
  margin: 24px;
  margin-top: 40px;
  height: 400px;
  overflow: auto;
  overscroll-behavior: contain;
  scroll-snap-type: y mandatory;
}

.custom-card-inside-container::-webkit-scrollbar {
  width: 5px;
}

.custom-card-inside-container::-webkit-scrollbar-track {
background-color: #17181C;
}

.custom-card-inside-container::-webkit-scrollbar-thumb {
  background: #4e4e4e;
  border-radius: 25px;
}

.career-preset-container {
  overflow: auto;
  width: 100%;
  height: calc(100vh - 685px);
  margin-top: 50px;
}

.career-preset-container::-webkit-scrollbar {
  width: 5px;
}

.career-preset-container::-webkit-scrollbar-track {
  background-color: black;
}

.career-preset-container::-webkit-scrollbar-thumb {
  background: #4e4e4e;
  border-radius: 25px;
}

html::-webkit-scrollbar {
  width: 5px;
}

html::-webkit-scrollbar-track {
  background-color: black;
}

html::-webkit-scrollbar-thumb {
  background: #4e4e4e;
  border-radius: 25px;
}

.keymap-content::-webkit-scrollbar {
  width: 5px;
}

.keymap-content::-webkit-scrollbar-track {
  background-color: black;
}

.keymap-content::-webkit-scrollbar-thumb {
  background: #4e4e4e;
  border-radius: 25px;
}

.switch-type-item {
  width: 215px;
  height: 100px;
  background: #0B0C0E;
  border: 2px solid #0B0C0E;
  opacity: 0.25;
  border-radius: 0.5em;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 200ms ease-in-out;
  padding: 0 1em;
}

.switch-type-item.active {
  opacity: 1;
  border: 2px solid #1A6AE0;
}

.switch-type-item:hover {
  border: 2px solid #bbbbbb;
}

.home-get-start {
  margin-top: 64px;
  display: inline-block;
  padding: 7px 50px;
  font-size: 20px;
  text-align: center;
  text-decoration: none;
  border: 2px solid #ffffff;
  border-radius: 8px;
  color: #ffffff;
  background-color: transparent;
  cursor: pointer;
  transition: all .3s ease-in-out;
}

.home-get-start:hover {
  background-color: #ffffff;
  color: #000000;
}

.ant-dropdown-trigger {
  color: #FFFFFF !important;
}

.ant-card-body {
  height: 200px;
}

progress.ant-notification-notice-progress{
   background: #52c41a !important;
   color: #52c41a;
}
.contentschedule{
  max-width: 400px;
}
.main_menu .ant-menu-item-divider{
  margin: 29px 0px;
  border: 1px solid var(---divider, #FFFFFF1A);
  width: 265px;
  gap: 10px;
}
.left_menu_bar{
  /* display: flex; */
  width: 19rem;
  height: 75rem;
  padding-top: 1rem;
  padding-right: 1.2rem;
  padding-bottom: 1rem;
  padding-left: 1.2rem;
  background: var(--background-background-1, #000);
}
.main_menu{
  background: var(--background-background-1, #000);
  margin-top: 1.5em;
  font-size: 18px
}
.main_menu svg{
  /* margin-right: 15px;
  margin-left: -6px */
}
.advanced_keyrs_title{
  display: flex;
  align-items: center;
  align-content: center;
  gap: 4px;
  align-self: stretch;
  flex-wrap: wrap;
}
.advanced_keyrs_title span{
  color: var(--text-text-1, #D7D7DB);
  font-family: vivoSans-Regular;
  font-size: 20px;
  font-style: normal;
  font-weight: 650;
  line-height: 24px; /* 120% */
}
.advanced_keyrs_content{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  align-self: stretch;
}
.advanced_keyrs_content span{
  color: var(--text-text-3, rgba(215, 215, 219, 0.40));
  font-family: vivoSans-Regular;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 150% */
}
.rs-config,.socd-config{
  display: flex;
  /* width: 1080px; */
  padding: 0px 30px 24px 30px;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  flex: 1 0 0;
}
.rs-config-content{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
}
.rs-config-item{
  display: flex;
  padding: 48px 0px;
  justify-content: center;
  align-items: center;
  gap: 40px;
  flex: 1 0 0;
  align-self: stretch;
}
.rs-config-key-container{
  display: flex;
  width: 252px;
  height: 88px;
  padding: 8px;
  align-items: center;
  border-radius: 8px;
  border: 1px dashed var(--text-text-3, rgba(215, 215, 219, 0.40));
  background: var(--background-background-2, #0F0F10);
}
.rs-config-key-container.active{
  border: 2px solid var(--Brand-Brand, #196BE5);
  background: var(--fill-new-fill-new-fill-6, #0F0F10);
}
.rs-config-key-choose{
  display: flex;
  width: 72px;
  height: 72px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border-radius: 4px;
  background: var(--netural-netural-15, #262626);
}
.rs-config-key-change{
  display: flex;
  padding: 0px 24px;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  align-self: stretch;
}
.rs-config-key-choose-icon{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(---HitBox, rgba(255, 255, 255, 0.00));
}
.rs-config-key-change-text{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
.rs-config-key-change-text span{
  color: var(--netural-netural-85, #D9D9D9);
  font-family: vivoSans-Regular;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 137.5% */
}
.socd-config-modes{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  align-self: stretch;
}
.socd-config-modes-bottom{
  display: flex;
  align-items: flex-start;
  gap: 40px;
  flex: 1 0 0;
  align-self: stretch;
}
.socd-config-modes-items{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 14px;
  align-self: stretch;
}
.alert_fill-4{
  border-radius: 4px;
  background: var(--fill-fill-4, rgba(239, 240, 245, 0.04));
}
.socd-config-alert{
  display: flex;
  padding: 9px 16px;
  align-items: flex-start;
  gap: 10px;
  flex: 1 0 0;
  width: 100%;
}
.socd-config-content{
  display: flex;
  align-items: center;
  gap: 10px;
  align-self: stretch;
}
.socd-config-icon{
  display: flex;
  padding: 2px;
  align-items: flex-start;
  gap: 10px;
}
.socd-config-content span{
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.socd-config-content span.text-3{
  color: var(--text-text-3, rgba(215, 215, 219, 0.40));
}
.socd-config-modes-items-cards{
  display: flex;
  align-items: center;
  gap: 16px;
  align-self: stretch;
}
.modes-items-card{
  display: flex;
  height: 80px;
  padding: 8px 20px;
  align-items: center;
  gap: 22px;
  flex: 1 0 0;
  border-radius: 4px;
  border: 1px solid var(--fill-fill-3, rgba(239, 240, 245, 0.08));
  background: var(--fill-fill-3, rgba(239, 240, 245, 0.08));
}
.modes-items-card.active{
  border-radius: 4px;
  background: var(--Brand-Brand, #196BE5);
}
.modes-item-text{
  display: flex;
  align-items: center;
  align-content: center;
  gap: 4px;
  flex: 1 0 0;
  flex-wrap: wrap;
}
.modes-item-text span{
  color: #EFF0F5;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 150% */
}
.modes-item-checked{
  display: flex;
  align-items: center;
  gap: 10px;
}
.modes-item-checked-icon{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(---HitBox, rgba(255, 255, 255, 0.00));
}
.modes-bottom-items{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 40px;
  flex: 1 0 0;
}
.advanced_socdmodes_title{
  display: flex;
  align-items: center;
  align-content: center;
  gap: 4px;
  flex: 1 0 0;
  flex-wrap: wrap;
}
.dks-config-items{
  display: flex;
  padding: 16px 0px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  align-self: stretch;
}
.dks-config-items-tops{
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  align-self: stretch;
}
.dks-config-items-container-left{
  display: flex;
  width: 115px;
  height: 48px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
}
.dks-config-items-container{
  display: flex;
  max-width: 572px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  gap: 24px;
  flex: 1 0 0;
}
.dks-config-top-item{
  display: flex;
  width: 143px;
  height: 46px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.dks-config-top-item-title{
  display: flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  gap: 4px;
  align-self: stretch;
  flex-wrap: wrap;
}
.dks-config-top-item-title span{
  color: var(--text-text-1, #D7D7DB);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 171.429% */
}
.dks-config-top-item-inputs{
  display: flex;
  width: 63px;
  height: 34px;
  justify-content: flex-end;
  align-items: center;
  flex-shrink: 0;
}
.dks-config-top-item-input{
  display: flex;
  padding: 4px;
  justify-content: center;
  align-items: center;
  align-self: stretch;
}
.dks-config-top-item-input-title{
  display: flex;
  align-items: center;
  align-content: center;
  flex-wrap: wrap;
}
.dks-config-top-item-input-title span{
  color: var(--text-text-3, rgba(215, 215, 219, 0.40));
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 171.429% */
}
.dks-config-top-item-input input{
  display: flex;
  padding: 1px 0px;
  justify-content: center;
  align-items: center;
  color: var(--text-text-1, #D7D7DB);
  font-size: 14px;
  font-style: normal;
  font-weight: 550;
  line-height: 22px; /* 157.143% */
}
.dks-config-item-container{
  display: flex;
  height: 46px;
  justify-content: center;
  align-items: center;
  align-self: stretch;
}
.dks-config-top-item-text{
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  align-self: stretch;
  height: 34px;
}

.dks-config-top-item-text span{
  color: var(---, var(--text-4, rgba(215, 215, 219, 0.25)));
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px; /* 157.143% */
  text-decoration-line: underline;
  text-decoration-color: var(---, var(--text-4, rgba(215, 215, 219, 0.25)));
  text-decoration-style: solid;
  text-decoration-skip-ink: none;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
  white-space: pre;
}
span.dks-config-top-item-text-number{
  color: var(--text-text-1, #D7D7DB);
}
.dks-config-items-main{
  display: flex;
  padding-left: 120px;
  align-items: center;
  flex: 1 0 0;
  align-self: stretch;
  gap: 16px
}
.dks-config-items-main-items{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
}
.dks-config-items-main-item{
  display: flex;
  align-items: center;
  gap: 28px;
}
.dks-config-items-main-item-text{
  display: flex;
  align-items: center;
  align-content: center;
  gap: 4px;
  flex-wrap: wrap;
}
.dks-config-items-main-item-text span{
  color: var(--text-text-3, rgba(215, 215, 219, 0.40));
  font-family: "vivo Sans";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 171.429% */
}
.dks-config-items-main-item-key-container{
  display: flex;
  width: 56px;
  height: 56px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  aspect-ratio: 1/1;
  border-radius: 3px;
  overflow-wrap: break-word;
  word-break: break-all;
  border: 1px dashed var(--stroke-stroke-3, rgba(255, 255, 255, 0.30));
}
.dks-config-items-main-item-key-container.active{
  border-radius: 3px;
  border: 2px solid var(--Brand-Brand, #196BE5);
  background: var(--fill-fill-4, rgba(239, 240, 245, 0.04));
}
.dks-config-items-main-container{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  flex: 1 0 0;
}
.dks-config-items-main-container-items{
  display: flex;
  max-width: 572px;
  padding: 0px 40px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 24px;
  flex: 1 0 0;
  align-self: stretch;
}
.dks-config-items-main-container-item{
  display: flex;
  height: 48px;
  padding: 0px 10px;
  align-items: center;
  gap: -24px;
  align-self: stretch;
}
.dks-config{
  display: flex;
  padding: 0px 36px 0px 36px;
  flex-direction: column;
  align-items: flex-start;
  gap: 14px;
  flex: 1 0 0;
  align-self: stretch;
}
.mt-config-content{
  display: flex;
  width: 960px;
  flex-direction: column;
  align-items: flex-start;
  gap: 60px;
}
.mt-config-content-bottom{
  display: flex;
  width: 753px;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}
.mt-config-content-slider{
  display: flex;
  padding-right: 8px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}
.dks-config-items-main-item.active{
  border-radius: 3px;
  border: 2px solid var(--Brand-Brand, #196BE5);
  background: var(--fill-fill-4, rgba(239, 240, 245, 0.04));
}