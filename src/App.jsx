import { useState, useContext } from 'react'
import './App.css'
import React from 'react';

import { theme, ConfigProvider ,Tooltip} from 'antd';
import { KeyboardProvider } from './components/Keyboard/KeyboardContext';
import { HandleDeviceProvider } from './components/HIDDevice/HandleDeviceContext';
import LayoutRoot from './components/Layout/LayoutRoot';
import { useTranslation } from 'react-i18next';

const App = () => {
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const { t } = useTranslation();

  React.useEffect(() => {
    if (import.meta.env.VITE_APP_ENV !== 'Dev') {
      if (window.location.protocol === 'http:') {
        window.location.href = window.location.href.replace('http:', 'https:');
      }
    }
  }, []);

  React.useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
  }, []);

  React.useEffect(() => {
    document.title = t('meta.title')
  }, [t])

  return (
    <ConfigProvider theme={{
      algorithm: theme.darkAlgorithm,
      "components": {
        "Menu": {
          "darkItemSelectedBg": "#17181C",
        }
      },
    }}>
      <KeyboardProvider>
        <HandleDeviceProvider>
          <div style={{display: windowWidth >= 1000 ? 'block' : 'none'}}>
            <LayoutRoot  />
          </div>
          <div style={{display: windowWidth >= 1000 ? 'none' : 'flex'}}>
            <div style={{
              height: '100vh',
              width: '100vw',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '20px',
              textAlign: 'center',
              background: "#161720",
              color: 'white'
            }}>
              请通过调整浏览器窗口大小为我们提供更多空间，或者切换到屏幕更大的设备。
            </div>
          </div>
        </HandleDeviceProvider>
      </KeyboardProvider>
    </ConfigProvider>
  );
};
export default App;
